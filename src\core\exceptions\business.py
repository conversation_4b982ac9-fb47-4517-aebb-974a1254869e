"""
业务异常类

定义与业务逻辑相关的异常类型。
"""

from typing import Any, Dict, List, Optional

from .base import ErrorCode, ErrorSeverity, LotterySystemException


class PredictionException(LotterySystemException):
    """预测相关异常"""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.PREDICTION_FAILED,
        model_name: Optional[str] = None,
        prediction_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if model_name:
            details['model_name'] = model_name
        if prediction_data:
            details['prediction_data'] = prediction_data
        
        kwargs['details'] = details
        super().__init__(message, code, **kwargs)


class ModelNotReadyException(PredictionException):
    """模型未就绪异常"""
    
    def __init__(self, model_name: str, reason: str = "模型尚未训练完成", **kwargs):
        message = f"模型 {model_name} 未就绪: {reason}"
        super().__init__(
            message,
            code=ErrorCode.MODEL_NOT_READY,
            model_name=model_name,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class InsufficientDataException(PredictionException):
    """数据不足异常"""
    
    def __init__(
        self,
        required_count: int,
        actual_count: int,
        data_type: str = "历史数据",
        **kwargs
    ):
        message = f"{data_type}不足: 需要{required_count}条，实际{actual_count}条"
        details = {
            'required_count': required_count,
            'actual_count': actual_count,
            'data_type': data_type
        }
        super().__init__(
            message,
            code=ErrorCode.INSUFFICIENT_DATA,
            details=details,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class DataValidationException(LotterySystemException):
    """数据验证异常"""
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        validation_rules: Optional[List[str]] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if field_name:
            details['field_name'] = field_name
        if field_value is not None:
            details['field_value'] = field_value
        if validation_rules:
            details['validation_rules'] = validation_rules
        
        kwargs['details'] = details
        super().__init__(
            message,
            code=ErrorCode.DATA_VALIDATION_ERROR,
            severity=ErrorSeverity.LOW,
            **kwargs
        )


class InvalidLotteryNumberException(DataValidationException):
    """无效彩票号码异常"""
    
    def __init__(self, number: str, **kwargs):
        message = f"无效的彩票号码: {number}"
        super().__init__(
            message,
            field_name="lottery_number",
            field_value=number,
            validation_rules=["必须是3位数字", "每位数字范围0-9"],
            **kwargs
        )


class InvalidDateRangeException(DataValidationException):
    """无效日期范围异常"""
    
    def __init__(self, start_date: str, end_date: str, **kwargs):
        message = f"无效的日期范围: {start_date} 到 {end_date}"
        super().__init__(
            message,
            field_name="date_range",
            field_value={"start": start_date, "end": end_date},
            validation_rules=["开始日期不能晚于结束日期", "日期格式必须为YYYY-MM-DD"],
            **kwargs
        )


class ModelException(LotterySystemException):
    """模型相关异常"""
    
    def __init__(
        self,
        message: str,
        model_name: Optional[str] = None,
        model_version: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if model_name:
            details['model_name'] = model_name
        if model_version:
            details['model_version'] = model_version
        if operation:
            details['operation'] = operation
        
        kwargs['details'] = details
        super().__init__(
            message,
            code=ErrorCode.MODEL_LOAD_ERROR,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class ModelLoadException(ModelException):
    """模型加载异常"""
    
    def __init__(self, model_path: str, **kwargs):
        message = f"模型加载失败: {model_path}"
        super().__init__(
            message,
            operation="load",
            details={'model_path': model_path},
            **kwargs
        )


class ModelTrainingException(ModelException):
    """模型训练异常"""
    
    def __init__(
        self,
        model_name: str,
        epoch: Optional[int] = None,
        loss: Optional[float] = None,
        **kwargs
    ):
        message = f"模型训练失败: {model_name}"
        details = kwargs.get('details', {})
        if epoch is not None:
            details['epoch'] = epoch
        if loss is not None:
            details['loss'] = loss
        
        super().__init__(
            message,
            model_name=model_name,
            operation="training",
            details=details,
            **kwargs
        )


class ConfigurationException(LotterySystemException):
    """配置相关异常"""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        if config_value is not None:
            details['config_value'] = config_value
        
        kwargs['details'] = details
        super().__init__(
            message,
            code=ErrorCode.CONFIG_VALIDATION_ERROR,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class MissingConfigException(ConfigurationException):
    """配置缺失异常"""
    
    def __init__(self, config_key: str, **kwargs):
        message = f"缺少必要配置: {config_key}"
        super().__init__(
            message,
            code=ErrorCode.CONFIG_MISSING_ERROR,
            config_key=config_key,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class InvalidConfigException(ConfigurationException):
    """无效配置异常"""
    
    def __init__(
        self,
        config_key: str,
        config_value: Any,
        expected_type: str,
        **kwargs
    ):
        message = f"配置值无效: {config_key}={config_value}, 期望类型: {expected_type}"
        super().__init__(
            message,
            config_key=config_key,
            config_value=config_value,
            details={'expected_type': expected_type},
            **kwargs
        )
