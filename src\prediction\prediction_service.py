#!/usr/bin/env python3
"""
预测服务

统一管理各种预测器，提供预测API接口
"""

import logging
import os
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.core.database import DatabaseManager
# 导入异常处理系统
from src.core.exceptions.business import PredictionException

from .base_predictor import BasePredictor
from .statistical_predictor import StatisticalPredictor

logger = logging.getLogger(__name__)

class PredictionService:
    """预测服务类"""
    
    def __init__(self, data_engine=None):
        """
        初始化预测服务
        
        Args:
            data_engine: 数据引擎实例
        """
        self.data_engine = data_engine
        self.db_manager = DatabaseManager() if not data_engine else data_engine.db_manager
        
        # 初始化预测器
        self.predictors: Dict[str, BasePredictor] = {}
        self._initialize_predictors()
        
        # 预测历史
        self.prediction_history: List[Dict[str, Any]] = []
        
        logger.info("预测服务初始化完成")
    
    def _initialize_predictors(self):
        """初始化预测器"""
        try:
            # 统计学预测器
            statistical_predictor = StatisticalPredictor()
            self.predictors["statistical"] = statistical_predictor
            
            logger.info(f"已初始化 {len(self.predictors)} 个预测器")
            
        except Exception as e:
            logger.error(f"初始化预测器失败: {e}")
    
    def train_all_predictors(self, force_retrain: bool = False) -> Dict[str, Any]:
        """
        训练所有预测器
        
        Args:
            force_retrain: 是否强制重新训练
            
        Returns:
            训练结果
        """
        logger.info("开始训练所有预测器")
        
        try:
            # 获取训练数据
            training_data = self._prepare_training_data()
            if not training_data:
                return {
                    "success": False,
                    "error": "无法获取训练数据",
                    "results": {}
                }
            
            results = {}
            
            for name, predictor in self.predictors.items():
                if force_retrain or not predictor.is_trained:
                    logger.info(f"训练预测器: {name}")
                    result = predictor.train(training_data)
                    results[name] = result
                else:
                    logger.info(f"预测器 {name} 已训练，跳过")
                    results[name] = {
                        "success": True,
                        "message": "已训练，跳过",
                        "last_training_time": predictor.last_training_time.isoformat() if predictor.last_training_time else None
                    }
            
            # 统计训练结果
            successful_count = sum(1 for r in results.values() if r.get("success", False))
            
            return {
                "success": True,
                "training_time": datetime.now().isoformat(),
                "data_count": len(training_data),
                "predictors_trained": successful_count,
                "total_predictors": len(self.predictors),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"训练预测器过程中发生错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": {}
            }
    
    def get_predictions(self, predictor_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        获取预测结果
        
        Args:
            predictor_names: 指定的预测器名称列表，None表示使用所有预测器
            
        Returns:
            预测结果
        """
        if predictor_names is None:
            predictor_names = list(self.predictors.keys())
        
        predictions = {}
        
        for name in predictor_names:
            if name in self.predictors:
                predictor = self.predictors[name]
                try:
                    result = predictor.predict()
                    predictions[name] = result
                    
                    # 记录预测历史
                    if result.get("success", False):
                        self._record_prediction(name, result)
                        
                except Exception as e:
                    logger.error(f"预测器 {name} 预测失败: {e}")
                    predictions[name] = {
                        "success": False,
                        "error": str(e),
                        "predictions": [],
                        "confidence": 0.0
                    }
            else:
                predictions[name] = {
                    "success": False,
                    "error": f"预测器 {name} 不存在",
                    "predictions": [],
                    "confidence": 0.0
                }
        
        return {
            "timestamp": datetime.now().isoformat(),
            "predictions": predictions,
            "summary": self._generate_prediction_summary(predictions)
        }
    
    def get_predictor_info(self) -> Dict[str, Any]:
        """
        获取预测器信息
        
        Returns:
            预测器信息
        """
        info = {}
        
        for name, predictor in self.predictors.items():
            info[name] = predictor.get_model_info()
        
        return {
            "total_predictors": len(self.predictors),
            "predictors": info,
            "service_info": {
                "prediction_history_count": len(self.prediction_history),
                "last_prediction_time": self.prediction_history[-1].get("timestamp") if self.prediction_history else None
            }
        }
    
    def get_prediction_history(self, limit: int = 10) -> Dict[str, Any]:
        """
        获取预测历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            预测历史
        """
        recent_history = self.prediction_history[-limit:] if self.prediction_history else []
        
        return {
            "history": recent_history,
            "total_count": len(self.prediction_history),
            "returned_count": len(recent_history)
        }
    
    def evaluate_predictions(self, start_date: Optional[str] = None, 
                           end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        评估预测准确率
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            评估结果
        """
        try:
            # 获取实际开奖结果
            actual_results = self._get_actual_results(start_date, end_date)
            
            if not actual_results:
                return {
                    "success": False,
                    "error": "无法获取实际开奖结果",
                    "evaluations": {}
                }
            
            evaluations = {}
            
            for name, predictor in self.predictors.items():
                if predictor.is_trained:
                    # 获取对应时期的预测结果
                    historical_predictions = self._get_historical_predictions(name, start_date, end_date)
                    
                    if historical_predictions:
                        accuracy = predictor.calculate_accuracy(historical_predictions, actual_results)
                        evaluations[name] = accuracy
                    else:
                        evaluations[name] = {
                            "accuracy": 0.0,
                            "message": "无历史预测数据"
                        }
                else:
                    evaluations[name] = {
                        "accuracy": 0.0,
                        "message": "预测器未训练"
                    }
            
            return {
                "success": True,
                "evaluation_period": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "actual_results_count": len(actual_results),
                "evaluations": evaluations
            }
            
        except Exception as e:
            logger.error(f"评估预测准确率失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "evaluations": {}
            }
    
    def _prepare_training_data(self) -> List[Dict[str, Any]]:
        """准备训练数据"""
        try:
            # 从数据库获取历史数据
            records = self.db_manager.get_all_records()
            
            training_data = []
            for record in records:
                training_data.append({
                    "period": record.period,
                    "date": record.date.isoformat() if record.date else None,
                    "numbers": record.numbers,
                    "trial_numbers": record.trial_numbers,
                    "sales": record.sales_amount,
                    "machine_number": record.draw_machine
                })
            
            logger.info(f"准备训练数据完成，共 {len(training_data)} 条记录")
            return training_data
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            return []
    
    def _record_prediction(self, predictor_name: str, prediction_result: Dict[str, Any]):
        """记录预测历史"""
        record = {
            "timestamp": datetime.now().isoformat(),
            "predictor": predictor_name,
            "predictions": prediction_result.get("predictions", []),
            "confidence": prediction_result.get("confidence", 0.0),
            "method": prediction_result.get("method", ""),
            "details": prediction_result.get("details", {})
        }
        
        self.prediction_history.append(record)
        
        # 限制历史记录数量
        if len(self.prediction_history) > 1000:
            self.prediction_history = self.prediction_history[-500:]
    
    def _generate_prediction_summary(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """生成预测摘要"""
        successful_predictions = [p for p in predictions.values() if p.get("success", False)]
        
        if not successful_predictions:
            return {
                "total_predictors": len(predictions),
                "successful_predictors": 0,
                "average_confidence": 0.0,
                "total_predictions": 0
            }
        
        total_confidence = sum(p.get("confidence", 0.0) for p in successful_predictions)
        average_confidence = total_confidence / len(successful_predictions)
        
        total_predictions = sum(len(p.get("predictions", [])) for p in successful_predictions)
        
        return {
            "total_predictors": len(predictions),
            "successful_predictors": len(successful_predictions),
            "average_confidence": round(average_confidence, 3),
            "total_predictions": total_predictions
        }
    
    def _get_actual_results(self, start_date: Optional[str], end_date: Optional[str]) -> List[Dict[str, Any]]:
        """获取实际开奖结果"""
        try:
            # 这里应该从数据库获取指定时期的实际开奖结果
            # 暂时返回空列表，后续可以完善
            return []
        except Exception as e:
            logger.error(f"获取实际开奖结果失败: {e}")
            return []
    
    def _get_historical_predictions(self, predictor_name: str, start_date: Optional[str], 
                                  end_date: Optional[str]) -> List[Dict[str, Any]]:
        """获取历史预测结果"""
        try:
            # 从预测历史中筛选指定预测器和时期的记录
            filtered_predictions = []
            
            for record in self.prediction_history:
                if record.get("predictor") == predictor_name:
                    # 这里可以添加日期筛选逻辑
                    filtered_predictions.append(record)
            
            return filtered_predictions
        except Exception as e:
            logger.error(f"获取历史预测结果失败: {e}")
            return []
