#!/usr/bin/env python3
"""
增强的SQLite数据库管理器

提供连接池、WAL模式、健康检查等高级功能
"""

import json
import logging
import sqlite3
import sys
import threading
import time
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from queue import Empty, Queue
from typing import Any, Dict, Generator, List, Optional

sys.path.append('src')

# 导入缓存系统
from core.cache import CacheManager, MemoryCache, cached
# 导入新的配置系统
from core.config import get_settings
# 导入异常处理系统
from core.exceptions.business import DataEngineException
from core.exceptions.technical import DatabaseConnectionException
from data.models import LotteryRecord

logger = logging.getLogger(__name__)

class ConnectionPool:
    """SQLite连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10, timeout: float = 30.0):
        """
        初始化连接池

        Args:
            db_path: 数据库文件路径
            max_connections: 最大连接数
            timeout: 连接超时时间（秒）
        """
        self.db_path = db_path
        self.max_connections = max_connections
        # 使用配置系统的超时设置
        settings = get_settings()
        self.timeout = timeout or settings.database.pool_timeout
        self._pool = Queue(maxsize=max_connections)
        self._lock = threading.Lock()
        self._created_connections = 0
        
        # 预创建一些连接
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        initial_size = min(3, self.max_connections)
        for _ in range(initial_size):
            conn = self._create_connection()
            if conn:
                self._pool.put(conn)
                self._created_connections += 1
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=self.timeout,
                check_same_thread=False
            )
            
            # 启用WAL模式
            conn.execute("PRAGMA journal_mode=WAL")
            
            # 优化设置
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB
            
            # 设置超时
            conn.execute(f"PRAGMA busy_timeout={int(self.timeout * 1000)}")
            
            return conn
        except Exception as e:
            logger.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self) -> Generator[sqlite3.Connection, None, None]:
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            # 尝试从池中获取连接
            try:
                conn = self._pool.get(timeout=5.0)
            except Empty:
                # 池中没有可用连接，尝试创建新连接
                with self._lock:
                    if self._created_connections < self.max_connections:
                        conn = self._create_connection()
                        if conn:
                            self._created_connections += 1
                    
                if not conn:
                    # 等待池中有可用连接
                    conn = self._pool.get(timeout=self.timeout)
            
            if not conn:
                raise DatabaseConnectionException("无法获取数据库连接，连接池可能已满")
            
            # 检查连接是否有效
            try:
                conn.execute("SELECT 1")
            except sqlite3.Error:
                # 连接无效，创建新连接
                conn.close()
                conn = self._create_connection()
                if not conn:
                    raise DatabaseConnectionException("无法创建有效的数据库连接，请检查数据库文件权限")
            
            yield conn
            
        finally:
            if conn:
                try:
                    # 回滚未提交的事务
                    conn.rollback()
                    # 将连接放回池中
                    self._pool.put(conn, timeout=1.0)
                except Exception as e:
                    logger.warning(f"归还连接到池时出错: {e}")
                    # 连接可能已损坏，关闭它
                    try:
                        conn.close()
                    except:
                        pass
                    
                    # 减少连接计数
                    with self._lock:
                        self._created_connections -= 1
    
    def close_all(self):
        """关闭所有连接"""
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except Empty:
                break
            except Exception as e:
                logger.warning(f"关闭连接时出错: {e}")
        
        with self._lock:
            self._created_connections = 0
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        return {
            "max_connections": self.max_connections,
            "created_connections": self._created_connections,
            "available_connections": self._pool.qsize(),
            "active_connections": self._created_connections - self._pool.qsize()
        }


class EnhancedDatabaseManager:
    """增强的数据库管理器"""
    
    def __init__(self, db_path: Optional[str] = None, max_connections: Optional[int] = None):
        """
        初始化增强数据库管理器

        Args:
            db_path: 数据库文件路径，如果为None则使用配置中的路径
            max_connections: 最大连接数，如果为None则使用配置中的值
        """
        # 获取配置
        settings = get_settings()

        # 使用配置中的数据库路径或传入的路径
        if db_path is None:
            self.db_path = Path(settings.get_database_url().replace("sqlite:///", ""))
        else:
            self.db_path = Path(db_path)

        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.logger = logger

        # 使用配置中的连接池大小
        max_conn = max_connections or settings.database.pool_size

        # 创建连接池
        self.pool = ConnectionPool(str(self.db_path), max_conn)

        # 初始化缓存管理器
        self.cache_manager = CacheManager([MemoryCache()])

        # 健康检查状态
        self._last_health_check = None
        self._health_status = {"healthy": False, "last_error": None}
        
        # 初始化数据库
        self._init_database()
        
        # 执行初始健康检查
        self.health_check()
    
    def _init_database(self) -> None:
        """初始化数据库结构"""
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建主数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lottery_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    period TEXT UNIQUE NOT NULL,
                    date DATE NOT NULL,
                    numbers TEXT NOT NULL,
                    trial_numbers TEXT NOT NULL,
                    draw_machine INTEGER NOT NULL,
                    trial_machine INTEGER NOT NULL,
                    sales_amount INTEGER NOT NULL,
                    direct_prize INTEGER NOT NULL,
                    group3_prize INTEGER NOT NULL,
                    group6_prize INTEGER NOT NULL,
                    unknown_field1 INTEGER DEFAULT 0,
                    unknown_field2 INTEGER DEFAULT 0,
                    unknown_field3 INTEGER DEFAULT 0,
                    sum_value INTEGER NOT NULL,
                    trial_sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    trial_span_value INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_period ON lottery_records(period)",
                "CREATE INDEX IF NOT EXISTS idx_date ON lottery_records(date)",
                "CREATE INDEX IF NOT EXISTS idx_numbers ON lottery_records(numbers)",
                "CREATE INDEX IF NOT EXISTS idx_sum_value ON lottery_records(sum_value)",
                "CREATE INDEX IF NOT EXISTS idx_span_value ON lottery_records(span_value)",
                "CREATE INDEX IF NOT EXISTS idx_sales_amount ON lottery_records(sales_amount)",
                "CREATE INDEX IF NOT EXISTS idx_draw_machine ON lottery_records(draw_machine)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            # 创建统计缓存表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS statistics_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cache_key TEXT UNIQUE NOT NULL,
                    cache_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    hit_count INTEGER DEFAULT 0
                )
            """)
            
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cache_key ON statistics_cache(cache_key)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_expires_at ON statistics_cache(expires_at)")
            
            # 创建元数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS metadata (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建健康检查表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS health_checks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT NOT NULL,
                    details TEXT,
                    response_time_ms INTEGER
                )
            """)
            
            conn.commit()
            self.logger.info("增强数据库初始化完成")

    def health_check(self) -> Dict[str, Any]:
        """执行数据库健康检查"""
        start_time = time.time()
        status = {"healthy": True, "checks": [], "response_time_ms": 0}

        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()

                # 检查1: 基本连接测试
                try:
                    cursor.execute("SELECT 1")
                    status["checks"].append({"name": "connection", "status": "ok"})
                except Exception as e:
                    status["healthy"] = False
                    status["checks"].append({"name": "connection", "status": "error", "error": str(e)})

                # 检查2: 表结构完整性
                try:
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    required_tables = ["lottery_records", "statistics_cache", "metadata", "health_checks"]

                    missing_tables = [t for t in required_tables if t not in tables]
                    if missing_tables:
                        status["healthy"] = False
                        status["checks"].append({
                            "name": "table_structure",
                            "status": "error",
                            "error": f"Missing tables: {missing_tables}"
                        })
                    else:
                        status["checks"].append({"name": "table_structure", "status": "ok"})
                except Exception as e:
                    status["healthy"] = False
                    status["checks"].append({"name": "table_structure", "status": "error", "error": str(e)})

                # 检查3: 数据完整性
                try:
                    cursor.execute("SELECT COUNT(*) FROM lottery_records")
                    record_count = cursor.fetchone()[0]

                    if record_count == 0:
                        status["checks"].append({"name": "data_integrity", "status": "warning", "message": "No records found"})
                    else:
                        status["checks"].append({"name": "data_integrity", "status": "ok", "record_count": record_count})
                except Exception as e:
                    status["healthy"] = False
                    status["checks"].append({"name": "data_integrity", "status": "error", "error": str(e)})

                # 检查4: WAL模式状态
                try:
                    cursor.execute("PRAGMA journal_mode")
                    journal_mode = cursor.fetchone()[0]

                    if journal_mode.upper() == "WAL":
                        status["checks"].append({"name": "wal_mode", "status": "ok"})
                    else:
                        status["checks"].append({
                            "name": "wal_mode",
                            "status": "warning",
                            "message": f"Journal mode is {journal_mode}, expected WAL"
                        })
                except Exception as e:
                    status["checks"].append({"name": "wal_mode", "status": "error", "error": str(e)})

                # 检查5: 连接池状态
                pool_status = self.pool.get_pool_status()
                if pool_status["created_connections"] > 0:
                    status["checks"].append({"name": "connection_pool", "status": "ok", "pool_info": pool_status})
                else:
                    status["healthy"] = False
                    status["checks"].append({"name": "connection_pool", "status": "error", "error": "No active connections"})

        except Exception as e:
            status["healthy"] = False
            status["checks"].append({"name": "general", "status": "error", "error": str(e)})

        # 计算响应时间
        response_time_ms = int((time.time() - start_time) * 1000)
        status["response_time_ms"] = response_time_ms

        # 更新健康状态
        self._last_health_check = datetime.now()
        self._health_status = {
            "healthy": status["healthy"],
            "last_error": None if status["healthy"] else str(status.get("checks", []))
        }

        # 记录健康检查结果
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO health_checks (status, details, response_time_ms)
                    VALUES (?, ?, ?)
                """, (
                    "healthy" if status["healthy"] else "unhealthy",
                    json.dumps(status["checks"]),
                    response_time_ms
                ))
                conn.commit()
        except Exception as e:
            self.logger.warning(f"记录健康检查结果失败: {e}")

        return status

    def get_health_status(self) -> Dict[str, Any]:
        """获取当前健康状态"""
        # 如果超过5分钟没有检查，执行新的健康检查
        if (not self._last_health_check or
            datetime.now() - self._last_health_check > timedelta(minutes=5)):
            return self.health_check()

        return self._health_status

    def insert_records(self, records: List[LotteryRecord]) -> int:
        """
        批量插入记录

        Args:
            records: 彩票记录列表

        Returns:
            插入的记录数
        """
        if not records:
            return 0

        with self.pool.get_connection() as conn:
            cursor = conn.cursor()

            insert_data = []
            for record in records:
                insert_data.append((
                    record.period,
                    record.date.isoformat(),
                    record.numbers,
                    record.trial_numbers,
                    record.draw_machine,
                    record.trial_machine,
                    record.sales_amount,
                    record.direct_prize,
                    record.group3_prize,
                    record.group6_prize,
                    record.unknown_field1,
                    record.unknown_field2,
                    record.unknown_field3,
                    record.sum_value,
                    record.trial_sum_value,
                    record.span_value,
                    record.trial_span_value
                ))

            cursor.executemany("""
                INSERT OR REPLACE INTO lottery_records (
                    period, date, numbers, trial_numbers, draw_machine, trial_machine,
                    sales_amount, direct_prize, group3_prize, group6_prize,
                    unknown_field1, unknown_field2, unknown_field3,
                    sum_value, trial_sum_value, span_value, trial_span_value
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, insert_data)

            conn.commit()
            inserted_count = cursor.rowcount

            self.logger.info(f"成功插入 {inserted_count} 条记录")
            return inserted_count

    @cached(ttl=60)  # 缓存1分钟
    def get_records_count(self) -> int:
        """获取记录总数"""
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM lottery_records")
            return cursor.fetchone()[0]

    def get_connection_pool_status(self) -> Dict[str, Any]:
        """获取连接池详细状态"""
        pool_status = self.pool.get_pool_status()

        # 添加数据库文件信息
        db_size = self.db_path.stat().st_size if self.db_path.exists() else 0

        return {
            **pool_status,
            "db_path": str(self.db_path),
            "db_size_mb": round(db_size / 1024 / 1024, 2),
            "last_health_check": self._last_health_check.isoformat() if self._last_health_check else None,
            "health_status": self._health_status
        }

    def optimize_database(self) -> Dict[str, Any]:
        """优化数据库性能"""
        start_time = time.time()
        results = {"operations": [], "total_time_ms": 0}

        with self.pool.get_connection() as conn:
            cursor = conn.cursor()

            # 1. 分析表统计信息
            try:
                cursor.execute("ANALYZE")
                results["operations"].append({"name": "analyze", "status": "completed"})
            except Exception as e:
                results["operations"].append({"name": "analyze", "status": "error", "error": str(e)})

            # 2. 清理过期缓存
            try:
                cursor.execute("""
                    DELETE FROM statistics_cache
                    WHERE expires_at IS NOT NULL AND expires_at <= ?
                """, (datetime.now().timestamp(),))
                deleted_cache = cursor.rowcount
                results["operations"].append({
                    "name": "clear_expired_cache",
                    "status": "completed",
                    "deleted_entries": deleted_cache
                })
            except Exception as e:
                results["operations"].append({"name": "clear_expired_cache", "status": "error", "error": str(e)})

            # 3. 清理旧的健康检查记录（保留最近1000条）
            try:
                cursor.execute("""
                    DELETE FROM health_checks
                    WHERE id NOT IN (
                        SELECT id FROM health_checks
                        ORDER BY check_time DESC
                        LIMIT 1000
                    )
                """)
                deleted_health = cursor.rowcount
                results["operations"].append({
                    "name": "cleanup_health_checks",
                    "status": "completed",
                    "deleted_entries": deleted_health
                })
            except Exception as e:
                results["operations"].append({"name": "cleanup_health_checks", "status": "error", "error": str(e)})

            # 4. 重建索引（如果需要）
            try:
                cursor.execute("REINDEX")
                results["operations"].append({"name": "reindex", "status": "completed"})
            except Exception as e:
                results["operations"].append({"name": "reindex", "status": "error", "error": str(e)})

            conn.commit()

        results["total_time_ms"] = int((time.time() - start_time) * 1000)
        return results

    def close(self):
        """关闭数据库管理器"""
        self.pool.close_all()
        self.logger.info("数据库管理器已关闭")
