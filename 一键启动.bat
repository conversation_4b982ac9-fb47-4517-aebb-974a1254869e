@echo off
chcp 65001 >nul
title 福彩3D预测系统 - 一键启动

echo.
echo ========================================
echo    福彩3D预测分析工具 - 一键启动
echo ========================================
echo.
echo 🚀 正在启动系统...
echo.

:: 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 错误：虚拟环境不存在！
    echo 请先运行以下命令创建虚拟环境：
    echo python -m venv venv
    echo venv\Scripts\activate
    echo pip install -e .
    pause
    exit /b 1
)

:: 检查数据库是否存在
if not exist "data\lottery.db" (
    echo ❌ 错误：数据库文件不存在！
    echo 请确保 data\lottery.db 文件存在
    pause
    exit /b 1
)

echo 📋 启动步骤：
echo 1. 启动API服务（后台运行）
echo 2. 等待5秒确保API服务启动完成
echo 3. 启动Streamlit界面
echo.

:: 第一步：启动API服务（后台运行）
echo 🔧 步骤1: 启动API服务...
echo 📍 命令：python start_production_api.py
echo 🌐 绑定：127.0.0.1:8888
start "福彩3D-API服务" cmd /k "title 福彩3D-API服务 && python start_production_api.py"

:: 第二步：等待API服务启动
echo ⏳ 步骤2: 等待API服务启动（5秒）...
timeout /t 5 /nobreak >nul

:: 验证API服务状态
echo 🔍 验证API服务状态...
curl -s http://127.0.0.1:8888/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ API服务启动成功
) else (
    echo ⚠️  API服务可能还在启动中，继续执行...
)

:: 第三步：启动Streamlit界面
echo >>> 步骤3: 启动Streamlit界面...
echo >>> 命令：python start_streamlit.py （推荐方式）
echo >>> 访问：http://127.0.0.1:8501
echo.
echo >>> 系统启动完成！
echo.
echo >>> 访问地址：
echo    - Streamlit界面: http://127.0.0.1:8501
echo    - API服务: http://127.0.0.1:8888/health
echo    - API文档: http://127.0.0.1:8888/docs
echo.
echo >>> 提示：
echo    - 关闭此窗口将停止Streamlit界面
echo    - API服务在独立窗口中运行
echo    - 如需停止所有服务，请关闭所有相关窗口
echo    - 首页应显示"API服务正常运行"且无警告信息
echo.

:: 使用推荐的启动方式（确保完整功能）
python start_streamlit.py

echo.
echo 👋 Streamlit界面已停止
echo 💡 API服务可能仍在后台运行，请手动关闭API服务窗口
pause
