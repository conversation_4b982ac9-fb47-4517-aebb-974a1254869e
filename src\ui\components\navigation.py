"""
导航组件模块
实现混合导航模式，解决Streamlit selectbox 10个选项限制问题
"""

from typing import Dict, List, Optional

import streamlit as st

from .user_preferences import UserPreferenceManager


class NavigationComponent:
    """混合导航组件类"""

    def __init__(self):
        """初始化导航组件"""
        self.pref_manager = UserPreferenceManager()
        self.function_categories = self._load_function_categories()
        # 使用时间戳和随机数生成更稳定的唯一ID
        import random
        import time
        timestamp = str(int(time.time() * 1000))[-8:]  # 取时间戳后8位
        random_part = str(random.randint(10000, 99999))
        self.instance_id = f"{timestamp}_{random_part}"
    
    def _load_function_categories(self) -> Dict[str, Dict[str, str]]:
        """
        加载功能分类映射

        Returns:
            功能分类字典
        """
        return {
            "🏠 首页概览": {
                "📈 数据概览": "show_data_overview",
                "🎲 最新开奖": "show_latest_draw",
                "📊 系统状态": "show_system_status"
            },
            "🎯 智能预测": {
                "🤖 智能融合预测": "show_intelligent_fusion",
                "📈 趋势分析预测": "show_trend_analysis",
                "🎯 预测分析": "show_prediction_analysis",
                "🏛️ 模型库管理": "show_model_library"
            },
            "📊 数据分析": {
                "🔍 数据查询": "show_data_query",
                "📊 频率分析": "show_frequency_analysis",
                "💰 销售分析": "show_sales_analysis",
                "📈 和值分布": "show_sum_distribution"
            },
            "🔧 系统管理": {
                "🔄 数据更新": "show_data_update",
                "📊 性能监控": "show_real_time_monitoring",
                "💡 优化建议": "show_optimization_suggestions",
                "🤖 模型训练": "show_training_monitoring"
            },
            "⚙️ 高级功能": {
                "🔬 特征工程": "show_feature_engineering",
                "🧪 A/B测试": "show_ab_testing",
                "📊 数据管理深度": "show_data_management_deep",
                "🔍 系统诊断": "show_bug_detection_status"
            }
        }
    
    def render_navigation(self) -> Optional[str]:
        """
        渲染导航界面

        Returns:
            选中的页面名称
        """
        # 检查是否有强制导航请求
        force_nav_key = f"force_navigation_{self.instance_id}"
        if force_nav_key in st.session_state:
            target_page = st.session_state[force_nav_key]
            del st.session_state[force_nav_key]  # 清除强制导航状态
            return target_page

        st.sidebar.markdown("### 📊 功能导航")

        # 添加页面切换动画效果的CSS
        st.markdown("""
        <style>
        .stSelectbox > div > div {
            transition: all 0.3s ease-in-out;
        }
        .stRadio > div {
            transition: all 0.3s ease-in-out;
        }
        .stButton > button {
            transition: all 0.2s ease-in-out;
        }
        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .element-container {
            transition: opacity 0.3s ease-in-out;
        }
        </style>
        """, unsafe_allow_html=True)

        # 导航模式选择 - 使用唯一的key避免冲突
        nav_mode = st.sidebar.radio(
            "选择导航模式",
            ["🎯 快速访问", "📋 分类浏览", "⭐ 我的收藏"],
            horizontal=True,
            key=f"nav_mode_selector_{self.instance_id}",
            help="选择不同的导航方式来访问功能页面"
        )

        # 保存导航模式偏好
        self.pref_manager.set_navigation_mode(nav_mode)

        # 根据模式渲染对应界面
        if nav_mode == "🎯 快速访问":
            return self._render_quick_access()
        elif nav_mode == "📋 分类浏览":
            return self._render_category_navigation()
        elif nav_mode == "⭐ 我的收藏":
            return self._render_favorites()

        return None
    
    def _render_quick_access(self) -> Optional[str]:
        """
        渲染快速访问模式
        
        Returns:
            选中的页面名称
        """
        st.sidebar.markdown("#### 🔥 常用功能")
        
        # 获取使用频率最高的页面
        frequent_pages = self.pref_manager.get_frequent_pages(6)
        
        if frequent_pages:
            # 显示使用频率最高的页面
            for page_name, usage_count in frequent_pages:
                col1, col2 = st.sidebar.columns([4, 1])
                with col1:
                    if st.button(
                        f"{page_name}",
                        key=f"freq_{page_name}_{self.instance_id}",
                        help=f"使用次数: {usage_count}",
                        use_container_width=True
                    ):
                        return page_name
                with col2:
                    # 收藏按钮
                    fav_icon = "⭐" if self.pref_manager.is_favorite(page_name) else "☆"
                    if st.button(fav_icon, key=f"fav_toggle_{page_name}_{self.instance_id}"):
                        self.pref_manager.toggle_favorite(page_name)
                        st.rerun()
        else:
            # 默认推荐页面
            st.sidebar.info("暂无使用记录，显示推荐功能")
            default_pages = [
                "📈 数据概览", 
                "🎯 预测分析", 
                "📊 实时监控",
                "💡 优化建议",
                "🔄 数据更新",
                "🔍 数据查询"
            ]
            
            for page in default_pages:
                col1, col2 = st.sidebar.columns([4, 1])
                with col1:
                    if st.button(
                        page,
                        key=f"default_{page}_{self.instance_id}",
                        use_container_width=True
                    ):
                        return page
                with col2:
                    fav_icon = "⭐" if self.pref_manager.is_favorite(page) else "☆"
                    if st.button(fav_icon, key=f"fav_default_{page}_{self.instance_id}"):
                        self.pref_manager.toggle_favorite(page)
                        st.rerun()
        
        # 显示最近访问
        recent_pages = self.pref_manager.get_recent_pages(3)
        if recent_pages:
            st.sidebar.markdown("#### 🕒 最近访问")
            for page in recent_pages:
                if st.sidebar.button(
                    f"↩️ {page}",
                    key=f"recent_{page}_{self.instance_id}",
                    use_container_width=True
                ):
                    return page
        
        return None
    
    def _render_category_navigation(self) -> Optional[str]:
        """
        渲染分类浏览模式
        
        Returns:
            选中的页面名称
        """
        st.sidebar.markdown("#### 📋 按分类浏览")
        
        # 主分类选择
        category = st.sidebar.selectbox(
            "选择功能分类",
            list(self.function_categories.keys()),
            key=f"category_selector_{self.instance_id}",
            help="选择功能分类来查看相关页面"
        )
        
        # 子功能选择
        if category:
            functions = list(self.function_categories[category].keys())
            
            st.sidebar.markdown(f"**{category}**")
            
            # 显示该分类下的所有功能
            for function_name in functions:
                col1, col2 = st.sidebar.columns([4, 1])
                with col1:
                    if st.button(
                        function_name,
                        key=f"cat_{function_name}_{self.instance_id}",
                        use_container_width=True
                    ):
                        return function_name
                with col2:
                    # 收藏按钮
                    fav_icon = "⭐" if self.pref_manager.is_favorite(function_name) else "☆"
                    if st.button(fav_icon, key=f"fav_cat_{function_name}_{self.instance_id}"):
                        self.pref_manager.toggle_favorite(function_name)
                        st.rerun()
        
        return None
    
    def _render_favorites(self) -> Optional[str]:
        """
        渲染收藏夹模式
        
        Returns:
            选中的页面名称
        """
        st.sidebar.markdown("#### ⭐ 收藏的功能")
        
        favorites = self.pref_manager.get_favorite_pages()
        
        if favorites:
            for page in favorites:
                col1, col2 = st.sidebar.columns([4, 1])
                with col1:
                    if st.button(
                        page,
                        key=f"fav_{page}_{self.instance_id}",
                        use_container_width=True
                    ):
                        return page
                with col2:
                    if st.button("❌", key=f"remove_fav_{page}_{self.instance_id}"):
                        self.pref_manager.toggle_favorite(page)
                        st.rerun()
        else:
            st.sidebar.info("暂无收藏的功能")
            st.sidebar.markdown("💡 **如何添加收藏？**")
            st.sidebar.markdown("在其他导航模式中点击 ☆ 按钮即可添加收藏")
            
            # 提供快速添加收藏的建议
            st.sidebar.markdown("**推荐收藏：**")
            recommended = ["📈 数据概览", "🎯 预测分析", "📊 实时监控"]
            for page in recommended:
                if st.sidebar.button(f"+ {page}", key=f"add_rec_{page}_{self.instance_id}"):
                    self.pref_manager.toggle_favorite(page)
                    st.rerun()
        
        return None
    
    def get_all_pages(self) -> List[str]:
        """
        获取所有可用页面列表
        
        Returns:
            所有页面名称列表
        """
        all_pages = []
        for category_pages in self.function_categories.values():
            all_pages.extend(category_pages.keys())
        return all_pages
    
    def get_page_category(self, page_name: str) -> Optional[str]:
        """
        获取页面所属分类
        
        Args:
            page_name: 页面名称
            
        Returns:
            页面所属分类名称
        """
        for category, pages in self.function_categories.items():
            if page_name in pages:
                return category
        return None
