#!/usr/bin/env python3
"""
启动生产版FastAPI服务
"""

import os
import sys
from pathlib import Path


# 检查是否在虚拟环境中
def check_virtual_env():
    """检查是否在虚拟环境中运行"""
    venv_path = Path(__file__).parent / "venv"
    if venv_path.exists():
        current_python = sys.executable
        venv_python = venv_path / "Scripts" / "python.exe"

        if not str(current_python).startswith(str(venv_path)):
            print("⚠️ 检测到虚拟环境但未激活")
            print(f"🔧 推荐使用: venv\\Scripts\\activate && python {Path(__file__).name}")
            print(f"🔧 或直接使用: venv\\Scripts\\python.exe {Path(__file__).name}")
            print("🚀 继续使用当前Python环境...")

check_virtual_env()

sys.path.append('src')

import uvicorn

from api.production_main import app

if __name__ == "__main__":
    print(">>> 启动生产版FastAPI服务...")
    print(">>> 绑定地址: 127.0.0.1:8888")
    print(">>> API文档: http://127.0.0.1:8888/docs")
    print(">>> 健康检查: http://127.0.0.1:8888/health")
    print(">>> 提示: API服务将在端口8888上运行")

    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8888,
        reload=False,
        log_level="info"
    )
