"""
JavaScript错误监控组件
创建日期: 2025年7月24日
用途: 在Streamlit页面中注入错误监控脚本，捕获并记录JavaScript错误
"""

import asyncio
import hashlib
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Any, Dict, Optional, Set

import streamlit as st
import streamlit.components.v1 as components

# 导入数据流追踪器
from .data_flow_tracer import (FlowStage, FlowStatus, add_trace_point,
                               complete_error_trace, start_error_trace)

logger = logging.getLogger(__name__)

class JavaScriptMonitor:
    """JavaScript错误监控器 - 增强版支持实时事件"""

    def __init__(self, database_manager=None, event_bus=None, log_level: str = 'ERROR'):
        """初始化JavaScript监控器

        Args:
            database_manager: 数据库管理器
            event_bus: 事件总线
            log_level: 日志级别 ('DEBUG', 'INFO', 'WARN', 'ERROR', 'SILENT')
        """
        self.db_manager = database_manager
        self.event_bus = event_bus
        self.session_id = self._get_session_id()

        # 错误去重缓存
        self.error_cache: Set[str] = set()
        self.cache_max_size = 1000

        # WebSocket配置
        self.websocket_enabled = True
        self.websocket_url = "ws://127.0.0.1:8888/ws/bug-detection"

        # 实时事件配置
        self.real_time_enabled = True
        self.batch_size = 5
        self.batch_timeout = 10  # 秒

        # 日志级别配置
        self.log_level = log_level.upper()
        self.log_levels = {
            'DEBUG': 0, 'INFO': 1, 'WARN': 2, 'ERROR': 3, 'SILENT': 4
        }
        self.log_level_value = self.log_levels.get(self.log_level, 3)  # 默认ERROR级别

    def _get_session_id(self) -> str:
        """获取或创建会话ID"""
        if 'bug_detection_session_id' not in st.session_state:
            st.session_state.bug_detection_session_id = str(uuid.uuid4())
        return st.session_state.bug_detection_session_id

    def _generate_error_hash(self, error_data: Dict[str, Any]) -> str:
        """生成错误哈希用于去重"""
        key_fields = [
            error_data.get('message', ''),
            error_data.get('filename', ''),
            str(error_data.get('lineno', 0)),
            str(error_data.get('colno', 0))
        ]
        hash_input = '|'.join(key_fields)
        return hashlib.md5(hash_input.encode()).hexdigest()

    def _is_duplicate_error(self, error_hash: str) -> bool:
        """检查是否为重复错误"""
        if error_hash in self.error_cache:
            return True

        # 添加到缓存
        self.error_cache.add(error_hash)

        # 限制缓存大小
        if len(self.error_cache) > self.cache_max_size:
            # 移除最旧的一半
            old_cache = list(self.error_cache)
            self.error_cache = set(old_cache[len(old_cache)//2:])

        return False
    
    def inject_error_monitor(self, page_name: str = "unknown") -> None:
        """注入增强版JavaScript错误监控脚本"""

        monitoring_script = f"""
        <script>
        // Bug检测系统 - 优化版JavaScript错误监控
        (function() {{
            const sessionId = '{self.session_id}';
            const pageName = '{page_name}';
            const apiEndpoint = 'http://127.0.0.1:8888/api/v1/bug-detection/js-error';
            const websocketUrl = '{self.websocket_url}';
            const realTimeEnabled = {str(self.real_time_enabled).lower()};
            const batchSize = {self.batch_size};
            const batchTimeout = {self.batch_timeout * 1000}; // 转换为毫秒
            const logLevel = '{self.log_level}';
            const logLevelValue = {self.log_level_value};

            // 日志级别定义
            const LOG_LEVELS = {{
                'DEBUG': 0, 'INFO': 1, 'WARN': 2, 'ERROR': 3, 'SILENT': 4
            }};

            // 优化的日志函数
            function bugLog(level, message, ...args) {{
                const levelValue = LOG_LEVELS[level] || 3;
                if (levelValue >= logLevelValue) {{
                    const prefix = level === 'ERROR' ? '❌' :
                                  level === 'WARN' ? '⚠️' :
                                  level === 'INFO' ? 'ℹ️' : '🔍';
                    console[level.toLowerCase()](prefix + ' Bug检测:', message, ...args);
                }}
            }}

            // 增强版错误收集器
            window.bugDetector = window.bugDetector || {{
                errors: [],
                errorBatch: [],
                sessionId: sessionId,
                pageName: pageName,
                websocket: null,
                batchTimer: null,
                errorHashes: new Set(),

                // 初始化监控
                init: function() {{
                    this.setupErrorHandlers();
                    if (realTimeEnabled) {{
                        this.initWebSocket();
                        this.startBatchTimer();
                    }}
                    bugLog('INFO', 'Bug检测系统已启动 - 实时监控模式');
                    bugLog('DEBUG', 'WebSocket URL:', websocketUrl);
                    bugLog('DEBUG', 'API Endpoint:', apiEndpoint);
                    bugLog('DEBUG', 'Real-time enabled:', realTimeEnabled);
                }},

                // 设置错误处理器
                setupErrorHandlers: function() {{
                    // 捕获JavaScript错误
                    window.onerror = (message, source, lineno, colno, error) => {{
                        this.reportError({{
                            type: 'javascript_error',
                            message: message,
                            filename: source,
                            lineno: lineno,
                            colno: colno,
                            stack_trace: error ? error.stack : null,
                            timestamp: Date.now() / 1000,
                            user_agent: navigator.userAgent,
                            url: window.location.href
                        }});
                        return false;
                    }};

                    // 捕获Promise rejection
                    window.addEventListener('unhandledrejection', (event) => {{
                        this.reportError({{
                            type: 'promise_rejection',
                            message: event.reason ? event.reason.toString() : 'Unhandled Promise Rejection',
                            filename: window.location.href,
                            stack_trace: event.reason && event.reason.stack ? event.reason.stack : null,
                            timestamp: Date.now() / 1000,
                            user_agent: navigator.userAgent,
                            url: window.location.href
                        }});
                    }});

                    // 捕获资源加载错误
                    window.addEventListener('error', (event) => {{
                        if (event.target !== window) {{
                            this.reportError({{
                                type: 'resource_error',
                                message: `Failed to load resource: ${{event.target.src || event.target.href}}`,
                                filename: event.target.src || event.target.href,
                                timestamp: Date.now() / 1000,
                                user_agent: navigator.userAgent,
                                url: window.location.href,
                                element_type: event.target.tagName
                            }});
                        }}
                    }}, true);

                    // 捕获网络错误
                    const originalFetch = window.fetch;
                    window.fetch = async (...args) => {{
                        try {{
                            const response = await originalFetch(...args);
                            if (!response.ok) {{
                                this.reportError({{
                                    type: 'network_error',
                                    message: `HTTP ${{response.status}}: ${{response.statusText}}`,
                                    filename: args[0],
                                    timestamp: Date.now() / 1000,
                                    user_agent: navigator.userAgent,
                                    url: window.location.href,
                                    status_code: response.status
                                }});
                            }}
                            return response;
                        }} catch (error) {{
                            this.reportError({{
                                type: 'network_error',
                                message: error.message,
                                filename: args[0],
                                stack_trace: error.stack,
                                timestamp: Date.now() / 1000,
                                user_agent: navigator.userAgent,
                                url: window.location.href
                            }});
                            throw error;
                        }}
                    }};
                }},

                // 初始化WebSocket连接
                initWebSocket: function() {{
                    try {{
                        this.websocket = new WebSocket(websocketUrl);

                        this.websocket.onopen = () => {{
                            bugLog('INFO', 'WebSocket连接已建立');
                            this.websocketConnected = true;

                            // 停止API轮询
                            this.stopApiPolling();

                            // 订阅Bug检测事件
                            this.websocket.send(JSON.stringify({{
                                type: 'subscribe',
                                topic: 'events:javascript_error'
                            }}));
                        }};

                        this.websocket.onmessage = (event) => {{
                            try {{
                                const data = JSON.parse(event.data);
                                this.handleWebSocketMessage(data);
                            }} catch (e) {{
                                bugLog('WARN', 'WebSocket消息解析失败:', e);
                            }}
                        }};

                        this.websocket.onclose = () => {{
                            bugLog('INFO', 'WebSocket连接已断开，尝试重连...');
                            this.websocketConnected = false;

                            // 启动API轮询降级机制
                            this.startApiPolling();

                            setTimeout(() => this.initWebSocket(), 5000);
                        }};

                        this.websocket.onerror = (error) => {{
                            bugLog('WARN', 'WebSocket错误，启动API轮询降级:', error);
                            this.websocketConnected = false;
                            this.startApiPolling();
                        }};
                    }} catch (e) {{
                        bugLog('WARN', 'WebSocket初始化失败:', e);
                    }}
                }},

                // 处理WebSocket消息
                handleWebSocketMessage: function(data) {{
                    if (data.type === 'event' && data.event_type === 'javascript_error') {{
                        bugLog('DEBUG', '收到实时Bug事件:', data);
                    }} else if (data.type === 'alert') {{
                        bugLog('WARN', '收到Bug告警:', data.message);
                    }}
                }},

                // API轮询降级机制
                apiPollingInterval: null,
                apiPollingEnabled: false,
                websocketConnected: false,

                startApiPolling: function() {{
                    if (this.apiPollingEnabled) return;

                    console.log('🔄 启动Bug检测API轮询降级机制');
                    this.apiPollingEnabled = true;

                    this.apiPollingInterval = setInterval(() => {{
                        this.fetchBugStatsViaApi();
                    }}, 15000); // 每15秒轮询一次

                    // 立即执行一次
                    this.fetchBugStatsViaApi();
                }},

                stopApiPolling: function() {{
                    if (!this.apiPollingEnabled) return;

                    console.log('⏹️ 停止Bug检测API轮询');
                    this.apiPollingEnabled = false;

                    if (this.apiPollingInterval) {{
                        clearInterval(this.apiPollingInterval);
                        this.apiPollingInterval = null;
                    }}
                }},

                fetchBugStatsViaApi: async function() {{
                    try {{
                        const response = await fetch('/api/v1/bug-detection/statistics');
                        if (response.ok) {{
                            const data = await response.json();
                            console.log('📊 通过API获取Bug统计数据:', data);

                            // 更新页面显示（如果有相关元素）
                            this.updateBugStatsDisplay(data);
                        }} else {{
                            console.warn('Bug统计API轮询失败:', response.status);
                        }}
                    }} catch (error) {{
                        console.warn('Bug统计API轮询错误:', error);
                    }}
                }},

                updateBugStatsDisplay: function(stats) {{
                    // 安全更新Bug统计显示
                    const errorCountElement = document.getElementById('error-count');
                    const errorTypesElement = document.getElementById('error-types');

                    if (errorCountElement && stats.total_bugs !== undefined) {{
                        errorCountElement.innerText = stats.total_bugs;
                    }}

                    if (errorTypesElement && stats.bug_types !== undefined) {{
                        errorTypesElement.innerText = stats.bug_types;
                    }}
                }},

                // 启动批处理定时器
                startBatchTimer: function() {{
                    this.batchTimer = setInterval(() => {{
                        if (this.errorBatch.length > 0) {{
                            this.sendBatch();
                        }}
                    }}, batchTimeout);
                }},

                // 生成错误哈希
                generateErrorHash: function(errorData) {{
                    const key = `${{errorData.message}}_${{errorData.filename}}_${{errorData.lineno}}_${{errorData.colno}}`;
                    return btoa(key).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
                }},

                // 报告错误（增强版）
                reportError: function(errorData) {{
                    // 生成错误哈希用于去重
                    const errorHash = this.generateErrorHash(errorData);

                    // 检查是否为重复错误
                    if (this.errorHashes.has(errorHash)) {{
                        return; // 跳过重复错误
                    }}

                    // 添加到去重缓存
                    this.errorHashes.add(errorHash);

                    // 限制缓存大小
                    if (this.errorHashes.size > 1000) {{
                        const oldHashes = Array.from(this.errorHashes);
                        this.errorHashes = new Set(oldHashes.slice(500));
                    }}

                    const fullErrorData = {{
                        ...errorData,
                        id: `js_error_${{Date.now()}}_${{Math.random().toString(36).substr(2, 9)}}`,
                        session_id: this.sessionId,
                        page_name: this.pageName,
                        error_hash: errorHash
                    }};

                    // 存储到本地数组
                    this.errors.push(fullErrorData);

                    // 添加到批处理队列
                    if (realTimeEnabled) {{
                        this.errorBatch.push(fullErrorData);

                        // 如果批次已满或是高优先级错误，立即发送
                        if (this.errorBatch.length >= batchSize || this.isHighPriorityError(errorData)) {{
                            this.sendBatch();
                        }}
                    }} else {{
                        // 传统方式发送
                        this.sendToBackend(fullErrorData);
                    }}

                    // 控制台输出
                    console.error('🐛 Bug检测:', fullErrorData);
                    console.log('📊 错误统计:', {{
                        total: this.errors.length,
                        batch_pending: this.errorBatch.length,
                        websocket_connected: this.websocket && this.websocket.readyState === WebSocket.OPEN
                    }});
                }},

                // 判断是否为高优先级错误
                isHighPriorityError: function(errorData) {{
                    const highPriorityTypes = ['javascript_error', 'promise_rejection'];
                    const criticalKeywords = ['uncaught', 'fatal', 'critical', 'security'];

                    if (highPriorityTypes.includes(errorData.type)) {{
                        return true;
                    }}

                    const message = (errorData.message || '').toLowerCase();
                    return criticalKeywords.some(keyword => message.includes(keyword));
                }},

                // 发送批处理
                sendBatch: function() {{
                    if (this.errorBatch.length === 0) return;

                    const batch = [...this.errorBatch];
                    this.errorBatch = [];

                    // 通过WebSocket发送（优先）
                    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {{
                        this.websocket.send(JSON.stringify({{
                            type: 'error_batch',
                            data: batch,
                            timestamp: Date.now() / 1000
                        }}));
                        console.log(`📤 通过WebSocket发送 ${{batch.length}} 个错误`);
                    }} else {{
                        // 降级到HTTP API
                        this.sendBatchToAPI(batch);
                    }}
                }},

                // 通过API发送批处理
                sendBatchToAPI: function(batch) {{
                    try {{
                        fetch(apiEndpoint + '/batch', {{
                            method: 'POST',
                            headers: {{
                                'Content-Type': 'application/json',
                            }},
                            body: JSON.stringify({{
                                errors: batch,
                                session_id: this.sessionId,
                                page_name: this.pageName
                            }})
                        }}).then(response => {{
                            if (response.ok) {{
                                console.log(`✅ 成功发送 ${{batch.length}} 个错误到API`);
                            }} else {{
                                console.warn('批处理发送失败:', response.status);
                            }}
                        }}).catch(err => {{
                            console.warn('批处理发送错误:', err);
                        }});
                    }} catch (e) {{
                        console.warn('批处理发送异常:', e);
                    }}
                }},

                // 发送单个错误到后端（降级方案）
                sendToBackend: function(errorData) {{
                    try {{
                        fetch(apiEndpoint, {{
                            method: 'POST',
                            headers: {{
                                'Content-Type': 'application/json',
                            }},
                            body: JSON.stringify(errorData)
                        }}).then(response => {{
                            if (!response.ok) {{
                                console.warn('单个错误发送失败');
                            }}
                        }}).catch(err => {{
                            console.warn('单个错误发送异常:', err);
                        }});
                    }} catch (e) {{
                        console.warn('发送错误报告失败:', e);
                    }}
                }},

                // 获取增强版错误统计
                getErrorStats: function() {{
                    const stats = {{
                        total: this.errors.length,
                        batch_pending: this.errorBatch.length,
                        websocket_connected: this.websocket && this.websocket.readyState === WebSocket.OPEN,
                        by_type: {{}},
                        recent_errors: this.errors.slice(-10)
                    }};

                    this.errors.forEach(error => {{
                        stats.by_type[error.type] = (stats.by_type[error.type] || 0) + 1;
                    }});

                    return stats;
                }},

                // 清除错误记录
                clearErrors: function() {{
                    this.errors = [];
                    this.errorBatch = [];
                    this.errorHashes.clear();
                }},

                // 手动触发批处理发送
                flushBatch: function() {{
                    this.sendBatch();
                }},

                // 获取连接状态
                getConnectionStatus: function() {{
                    return {{
                        websocket: this.websocket ? this.websocket.readyState : 'not_initialized',
                        real_time_enabled: realTimeEnabled,
                        batch_size: batchSize,
                        pending_errors: this.errorBatch.length
                    }};
                }}
            }};
            
            // 初始化监控
            if (document.readyState === 'loading') {{
                document.addEventListener('DOMContentLoaded', () => {{
                    window.bugDetector.init();
                }});
            }} else {{
                window.bugDetector.init();
            }}
            
            // 页面卸载时发送剩余错误
            window.addEventListener('beforeunload', () => {{
                if (window.bugDetector.errors.length > 0) {{
                    // 使用sendBeacon发送剩余数据
                    const data = JSON.stringify({{
                        type: 'batch_errors',
                        errors: window.bugDetector.errors,
                        sessionId: window.bugDetector.sessionId
                    }});
                    
                    if (navigator.sendBeacon) {{
                        navigator.sendBeacon(apiEndpoint, data);
                    }}
                }}
            }});
            
        }})();
        </script>
        """
        
        # 注入监控脚本
        components.html(monitoring_script, height=0)
        
        logger.info(f"JavaScript error monitor injected for page: {page_name}")

    async def publish_error_event(self, error_data: Dict[str, Any]) -> bool:
        """发布JavaScript错误事件到事件总线"""
        # 开始数据流追踪
        trace_id = start_error_trace(error_data)

        try:
            if not self.event_bus:
                add_trace_point(trace_id, FlowStage.EVENT_BUS_PUBLISH, FlowStatus.FAILED,
                              {}, "事件总线未初始化")
                complete_error_trace(trace_id, FlowStatus.FAILED)
                return False

            # 检查错误去重
            error_hash = self._generate_error_hash(error_data)
            if self._is_duplicate_error(error_hash):
                add_trace_point(trace_id, FlowStage.EVENT_BUS_PUBLISH, FlowStatus.FAILED,
                              {"error_hash": error_hash}, "重复错误，已过滤")
                complete_error_trace(trace_id, FlowStatus.FAILED)
                return False

            # 导入事件相关模块
            from ..realtime.event_bus import (EventPriority, EventType,
                                              publish_event)

            # 确定优先级
            priority = EventPriority.HIGH if self._is_high_priority_error(error_data) else EventPriority.MEDIUM

            # 发布事件
            success = await publish_event(
                EventType.JAVASCRIPT_ERROR,
                {
                    'session_id': self.session_id,
                    'error_hash': error_hash,
                    **error_data
                },
                priority,
                "javascript_monitor"
            )

            if success:
                add_trace_point(trace_id, FlowStage.EVENT_BUS_PUBLISH, FlowStatus.SUCCESS,
                              {"error_hash": error_hash, "priority": priority.value})
                logger.debug(f"JavaScript错误事件已发布: {error_hash}")
                complete_error_trace(trace_id, FlowStatus.SUCCESS)
            else:
                add_trace_point(trace_id, FlowStage.EVENT_BUS_PUBLISH, FlowStatus.FAILED,
                              {"error_hash": error_hash}, "事件发布失败")
                complete_error_trace(trace_id, FlowStatus.FAILED)

            return success

        except Exception as e:
            add_trace_point(trace_id, FlowStage.EVENT_BUS_PUBLISH, FlowStatus.FAILED,
                          {}, f"异常: {str(e)}")
            complete_error_trace(trace_id, FlowStatus.FAILED)
            logger.error(f"发布JavaScript错误事件失败: {e}")
            return False

    def _is_high_priority_error(self, error_data: Dict[str, Any]) -> bool:
        """判断是否为高优先级错误"""
        high_priority_types = ['javascript_error', 'promise_rejection']
        critical_keywords = ['uncaught', 'fatal', 'critical', 'security', 'cors']

        error_type = error_data.get('type', '')
        if error_type in high_priority_types:
            return True

        message = str(error_data.get('message', '')).lower()
        return any(keyword in message for keyword in critical_keywords)

    def get_real_time_config(self) -> Dict[str, Any]:
        """获取实时监控配置"""
        return {
            'real_time_enabled': self.real_time_enabled,
            'websocket_enabled': self.websocket_enabled,
            'websocket_url': self.websocket_url,
            'batch_size': self.batch_size,
            'batch_timeout': self.batch_timeout,
            'cache_size': len(self.error_cache),
            'cache_max_size': self.cache_max_size
        }

    def update_config(self, config: Dict[str, Any]) -> None:
        """更新实时监控配置"""
        if 'real_time_enabled' in config:
            self.real_time_enabled = config['real_time_enabled']
        if 'websocket_enabled' in config:
            self.websocket_enabled = config['websocket_enabled']
        if 'batch_size' in config:
            self.batch_size = max(1, min(100, config['batch_size']))
        if 'batch_timeout' in config:
            self.batch_timeout = max(1, min(60, config['batch_timeout']))

        logger.info(f"JavaScript监控配置已更新: {config}")

    def get_client_side_errors(self) -> str:
        """获取客户端错误的JavaScript代码"""
        return """
        <script>
        function getBugDetectionErrors() {
            if (window.bugDetector) {
                return {
                    errors: window.bugDetector.errors,
                    stats: window.bugDetector.getErrorStats(),
                    sessionId: window.bugDetector.sessionId
                };
            }
            return { errors: [], stats: {}, sessionId: null };
        }
        
        // 暴露给Streamlit
        window.getBugDetectionErrors = getBugDetectionErrors;
        </script>
        """
    
    def display_error_summary(self):
        """显示错误摘要（用于调试）"""
        if st.checkbox("显示JavaScript错误监控状态", key="js_error_debug"):
            st.subheader("🔍 JavaScript错误监控")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("会话ID", self.session_id)
                st.metric("监控状态", "✅ 活跃")
            
            with col2:
                # 显示客户端错误统计
                error_check_script = """
                <script>
                // 安全的DOM操作函数
                function safeSetText(elementId, text) {
                    const element = document.getElementById(elementId);
                    if (element) {
                        element.innerText = text;
                        return true;
                    } else {
                        console.warn('元素不存在:', elementId);
                        return false;
                    }
                }

                if (window.bugDetector) {
                    const stats = window.bugDetector.getErrorStats();
                    const errorCount = Object.values(stats).reduce((a, b) => a + b, 0);
                    safeSetText('error-count', errorCount);
                    safeSetText('error-types', Object.keys(stats).length);
                } else {
                    safeSetText('error-count', '监控未初始化');
                    safeSetText('error-types', 'N/A');
                }
                </script>
                <div>
                    <p>检测到的错误数: <span id="error-count">加载中...</span></p>
                    <p>错误类型数: <span id="error-types">加载中...</span></p>
                </div>
                """
                components.html(error_check_script, height=100)
    
    def create_test_error_button(self):
        """创建测试错误按钮（用于测试）"""
        if st.button("🧪 触发测试JavaScript错误", key="test_js_error"):
            test_error_script = """
            <script>
            // 触发一个测试错误
            setTimeout(() => {
                throw new Error('这是一个测试错误 - Bug检测系统正常工作');
            }, 100);
            </script>
            """
            components.html(test_error_script, height=0)
            st.success("测试错误已触发，请检查控制台和错误报告")

# 便捷函数
def inject_js_monitor(page_name: str = "unknown", database_manager=None, log_level: str = 'ERROR'):
    """便捷函数：注入JavaScript错误监控

    Args:
        page_name: 页面名称
        database_manager: 数据库管理器
        log_level: 日志级别 ('DEBUG', 'INFO', 'WARN', 'ERROR', 'SILENT')
    """
    monitor = JavaScriptMonitor(database_manager, log_level=log_level)
    monitor.inject_error_monitor(page_name)
    return monitor

def show_js_monitor_debug(log_level: str = 'ERROR'):
    """便捷函数：显示JavaScript监控调试信息

    Args:
        log_level: 日志级别 ('DEBUG', 'INFO', 'WARN', 'ERROR', 'SILENT')
    """
    monitor = JavaScriptMonitor(log_level=log_level)
    monitor.display_error_summary()
    monitor.create_test_error_button()
