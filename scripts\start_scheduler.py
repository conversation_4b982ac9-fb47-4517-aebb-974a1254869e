#!/usr/bin/env python3
"""
定时任务调度器启动脚本

用于启动和管理定时任务调度器
"""

import os
import signal
import sys
import time
from pathlib import Path


def check_virtual_env():
    """检查是否在虚拟环境中运行"""
    project_dir = Path(__file__).parent.parent
    venv_path = project_dir / "venv"

    if venv_path.exists():
        current_python = sys.executable

        if not str(current_python).startswith(str(venv_path)):
            print("[警告] 检测到虚拟环境但未激活")
            print(f"[建议] 推荐使用: venv\\Scripts\\activate && python {Path(__file__).relative_to(project_dir)}")
            print(f"[建议] 或直接使用: venv\\Scripts\\python.exe {Path(__file__).relative_to(project_dir)}")
            print("[信息] 继续使用当前Python环境...")


# 检查虚拟环境
check_virtual_env()

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / 'src'))

import argparse
import json
from datetime import datetime

# 导入配置系统
from core.config import get_settings
from scheduler.task_scheduler import APSCHEDULER_AVAILABLE, TaskScheduler


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在停止调度器...")
    if hasattr(signal_handler, 'scheduler'):
        signal_handler.scheduler.stop()
    sys.exit(0)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='福彩3D定时任务调度器')
    parser.add_argument('--config', '-c', default='scheduler_config.json', help='配置文件路径')
    parser.add_argument('--data-dir', default='data', help='数据目录路径')
    parser.add_argument('--daemon', '-d', action='store_true', help='后台运行模式')
    parser.add_argument('--status', '-s', action='store_true', help='查看调度器状态')
    parser.add_argument('--run-job', help='立即执行指定任务')
    parser.add_argument('--stop', action='store_true', help='停止调度器')
    parser.add_argument('--test', '-t', action='store_true', help='测试模式（不启动调度器）')
    
    args = parser.parse_args()
    
    # 设置工作目录
    script_dir = Path(__file__).parent
    project_dir = script_dir.parent
    os.chdir(project_dir)
    
    print("[启动] 福彩3D定时任务调度器")
    print(f"[目录] 工作目录: {project_dir}")
    print(f"[数据] 数据目录: {args.data_dir}")
    print(f"[配置] 配置文件: {args.config}")
    print(f"[时间] 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    # 检查APScheduler
    if not APSCHEDULER_AVAILABLE:
        print("[错误] APScheduler未安装，无法使用定时任务功能")
        print("请运行: pip install apscheduler")
        return
    
    try:
        # 创建调度器
        scheduler = TaskScheduler(args.data_dir, args.config)
        signal_handler.scheduler = scheduler
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 查看状态
        if args.status:
            print("[状态] 调度器状态:")
            status = scheduler.get_job_status()
            
            print(f"   运行状态: {'运行中' if status.get('running', False) else '已停止'}")
            print(f"   任务数量: {status.get('job_count', 0)}")
            
            jobs = status.get('jobs', [])
            if jobs:
                print(f"   任务列表:")
                for job in jobs:
                    next_run = job.get('next_run', 'N/A')
                    if next_run != 'N/A':
                        next_run = datetime.fromisoformat(next_run).strftime('%Y-%m-%d %H:%M:%S')
                    print(f"     - {job['name']} (ID: {job['id']})")
                    print(f"       下次执行: {next_run}")
                    print(f"       触发器: {job['trigger']}")
            else:
                print(f"   没有配置任务")
            
            return
        
        # 立即执行任务
        if args.run_job:
            print(f"[执行] 立即执行任务: {args.run_job}")
            success = scheduler.run_job_now(args.run_job)

            if success:
                print(f"[成功] 任务执行成功")
            else:
                print(f"[失败] 任务执行失败")
            
            return
        
        # 测试模式
        if args.test:
            print("[测试] 测试模式")

            # 测试配置加载
            print("   - 配置加载测试...")
            config = scheduler.config
            print(f"     [成功] 配置加载成功")

            # 测试任务函数
            print("   - 数据更新任务测试...")
            try:
                scheduler.data_update_job()
                print(f"     [成功] 数据更新任务测试成功")
            except Exception as e:
                print(f"     [失败] 数据更新任务测试失败: {e}")

            print("   - 文件清理任务测试...")
            try:
                scheduler.cleanup_job()
                print(f"     [成功] 文件清理任务测试成功")
            except Exception as e:
                print(f"     [失败] 文件清理任务测试失败: {e}")

            print("[完成] 测试完成")
            return
        
        # 启动调度器
        print("[启动] 启动定时任务调度器...")

        success = scheduler.start()
        if not success:
            print("[失败] 调度器启动失败")
            return

        print("[成功] 调度器启动成功")
        
        # 显示任务信息
        status = scheduler.get_job_status()
        jobs = status.get('jobs', [])
        
        if jobs:
            print(f"[任务] 已配置 {len(jobs)} 个定时任务:")
            for job in jobs:
                next_run = job.get('next_run', 'N/A')
                if next_run != 'N/A':
                    next_run = datetime.fromisoformat(next_run).strftime('%Y-%m-%d %H:%M:%S')
                print(f"   - {job['name']}: {next_run}")
        else:
            print("[警告] 没有配置任务")
        
        # 后台运行模式
        if args.daemon:
            print("[后台] 进入后台运行模式...")
            print("按 Ctrl+C 停止调度器")

            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n[停止] 收到停止信号")
        else:
            print("[信息] 调度器已在后台启动")
            print("使用 --status 查看状态")
            print("使用 --stop 停止调度器")

    except KeyboardInterrupt:
        print(f"\n[中断] 用户中断操作")
    except Exception as e:
        print(f"\n[错误] 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # 确保调度器停止
        if 'scheduler' in locals():
            scheduler.stop()


if __name__ == "__main__":
    main()
