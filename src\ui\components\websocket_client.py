#!/usr/bin/env python3
"""
WebSocket客户端组件
创建日期: 2025年7月24日
用途: 实现Streamlit WebSocket集成、实时数据更新、连接状态管理
"""

import json
import logging
import time
import uuid
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

import streamlit as st
import streamlit.components.v1 as components

logger = logging.getLogger(__name__)

class StreamlitWebSocketClient:
    """Streamlit WebSocket客户端"""

    # 日志级别定义
    LOG_LEVELS = {
        'DEBUG': 0,
        'INFO': 1,
        'WARN': 2,
        'ERROR': 3,
        'SILENT': 4
    }

    def __init__(self, websocket_url: str = "ws://127.0.0.1:8888/ws/bug-detection", log_level: str = 'WARN'):
        self.websocket_url = websocket_url
        self.client_id = str(uuid.uuid4())
        self.connection_status = "disconnected"
        self.message_handlers: Dict[str, Callable] = {}
        self.received_messages: List[Dict[str, Any]] = []
        self.max_messages = 100
        self.log_level = log_level.upper()
        self.log_level_value = self.LOG_LEVELS.get(self.log_level, 2)  # 默认WARN级别

        # 初始化会话状态
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'websocket_client_id' not in st.session_state:
            st.session_state.websocket_client_id = self.client_id
        
        if 'websocket_messages' not in st.session_state:
            st.session_state.websocket_messages = []
        
        if 'websocket_connection_status' not in st.session_state:
            st.session_state.websocket_connection_status = "disconnected"
        
        if 'websocket_last_ping' not in st.session_state:
            st.session_state.websocket_last_ping = 0
    
    def inject_websocket_client(self):
        """注入WebSocket客户端JavaScript代码"""
        
        websocket_script = f"""
        <script>
        // Streamlit WebSocket客户端 - 优化版
        (function() {{
            const websocketUrl = '{self.websocket_url}';
            const clientId = '{self.client_id}';
            const logLevel = '{self.log_level}';
            const logLevelValue = {self.log_level_value};
            let websocket = null;
            let reconnectAttempts = 0;
            let maxReconnectAttempts = 5;
            let reconnectDelay = 1000; // 1秒
            let pingInterval = null;

            // 日志级别定义
            const LOG_LEVELS = {{
                'DEBUG': 0, 'INFO': 1, 'WARN': 2, 'ERROR': 3, 'SILENT': 4
            }};

            // 优化的日志函数
            function wsLog(level, message, ...args) {{
                const levelValue = LOG_LEVELS[level] || 1;
                if (levelValue >= logLevelValue) {{
                    const prefix = level === 'ERROR' ? '❌' :
                                  level === 'WARN' ? '⚠️' :
                                  level === 'INFO' ? 'ℹ️' : '🔍';
                    console[level.toLowerCase()](prefix + ' WebSocket:', message, ...args);
                }}
            }}

            // WebSocket客户端管理器
            window.streamlitWebSocket = {{
                socket: null,
                connected: false,
                messageHandlers: {{}},
                messageQueue: [],
                
                // 初始化连接
                init: function() {{
                    this.connect();
                    wsLog('INFO', 'Streamlit WebSocket客户端已初始化');
                }},

                // 建立WebSocket连接
                connect: function() {{
                    try {{
                        this.socket = new WebSocket(websocketUrl);

                        this.socket.onopen = (event) => {{
                            wsLog('INFO', 'WebSocket连接已建立');
                            this.connected = true;
                            reconnectAttempts = 0;
                            
                            // 发送客户端信息
                            this.send({{
                                type: 'client_info',
                                client_id: clientId,
                                user_agent: navigator.userAgent,
                                timestamp: Date.now() / 1000
                            }});
                            
                            // 启动心跳
                            this.startPing();
                            
                            // 更新Streamlit状态
                            this.updateStreamlitState('connected');
                            
                            // 处理消息队列
                            this.processMessageQueue();
                        }};
                        
                        this.socket.onmessage = (event) => {{
                            try {{
                                const data = JSON.parse(event.data);
                                this.handleMessage(data);
                            }} catch (e) {{
                                wsLog('WARN', 'WebSocket消息解析失败:', e);
                            }}
                        }};

                        this.socket.onclose = (event) => {{
                            wsLog('INFO', 'WebSocket连接已断开');
                            this.connected = false;
                            this.stopPing();
                            this.updateStreamlitState('disconnected');

                            // 自动重连
                            if (reconnectAttempts < maxReconnectAttempts) {{
                                setTimeout(() => {{
                                    reconnectAttempts++;
                                    wsLog('INFO', `尝试重连 (${{reconnectAttempts}}/${{maxReconnectAttempts}})`);
                                    this.connect();
                                }}, reconnectDelay * reconnectAttempts);
                            }} else {{
                                wsLog('ERROR', 'WebSocket重连失败，已达到最大重试次数');
                                this.updateStreamlitState('failed');
                            }}
                        }};
                        
                        this.socket.onerror = (error) => {{
                            wsLog('ERROR', 'WebSocket错误:', error);
                            this.updateStreamlitState('error');
                        }};

                    }} catch (e) {{
                        wsLog('ERROR', 'WebSocket连接失败:', e);
                        this.updateStreamlitState('error');
                    }}
                }},

                // 发送消息
                send: function(message) {{
                    if (this.connected && this.socket.readyState === WebSocket.OPEN) {{
                        this.socket.send(JSON.stringify(message));
                        return true;
                    }} else {{
                        // 添加到消息队列
                        this.messageQueue.push(message);
                        wsLog('WARN', 'WebSocket未连接，消息已加入队列');
                        return false;
                    }}
                }},
                
                // 处理消息队列
                processMessageQueue: function() {{
                    while (this.messageQueue.length > 0 && this.connected) {{
                        const message = this.messageQueue.shift();
                        this.send(message);
                    }}
                }},
                
                // 处理接收到的消息
                handleMessage: function(data) {{
                    wsLog('DEBUG', '收到WebSocket消息:', data);

                    // 更新Streamlit消息列表
                    this.addToStreamlitMessages(data);

                    // 调用消息处理器
                    const messageType = data.type;
                    if (this.messageHandlers[messageType]) {{
                        this.messageHandlers[messageType](data);
                    }}

                    // 处理特殊消息类型
                    if (messageType === 'pong') {{
                        this.handlePong(data);
                    }} else if (messageType === 'event') {{
                        this.handleEvent(data);
                    }} else if (messageType === 'alert') {{
                        this.handleAlert(data);
                    }}
                }},

                // 处理事件消息
                handleEvent: function(data) {{
                    // 可以在这里添加特定的事件处理逻辑
                    wsLog('DEBUG', '收到事件:', data.event_type);
                }},
                
                // 处理告警消息
                handleAlert: function(data) {{
                    wsLog('WARN', '收到告警:', data.message);

                    // 可以在这里添加告警通知逻辑
                    if (Notification && Notification.permission === 'granted') {{
                        new Notification('Bug检测告警', {{
                            body: data.message,
                            icon: '/favicon.ico'
                        }});
                    }}
                }},
                
                // 启动心跳
                startPing: function() {{
                    pingInterval = setInterval(() => {{
                        if (this.connected) {{
                            this.send({{
                                type: 'ping',
                                timestamp: Date.now() / 1000
                            }});
                        }}
                    }}, 30000); // 30秒心跳
                }},
                
                // 停止心跳
                stopPing: function() {{
                    if (pingInterval) {{
                        clearInterval(pingInterval);
                        pingInterval = null;
                    }}
                }},
                
                // 处理心跳响应
                handlePong: function(data) {{
                    const now = Date.now() / 1000;
                    const latency = now - data.timestamp;
                    wsLog('DEBUG', `心跳延迟: ${{latency.toFixed(3)}}s`);
                }},
                
                // 订阅事件类型
                subscribe: function(eventType) {{
                    this.send({{
                        type: 'subscribe',
                        topic: `events:${{eventType}}`
                    }});
                }},
                
                // 取消订阅
                unsubscribe: function(eventType) {{
                    this.send({{
                        type: 'unsubscribe',
                        topic: `events:${{eventType}}`
                    }});
                }},
                
                // 注册消息处理器
                onMessage: function(messageType, handler) {{
                    this.messageHandlers[messageType] = handler;
                }},
                
                // 更新Streamlit状态
                updateStreamlitState: function(status) {{
                    // 通过自定义事件通知Streamlit
                    window.dispatchEvent(new CustomEvent('websocket_status_change', {{
                        detail: {{ status: status, timestamp: Date.now() / 1000 }}
                    }}));

                    // 如果WebSocket连接失败，启动API轮询降级机制
                    if (status === 'failed' || status === 'error') {{
                        this.startApiPolling();
                    }} else if (status === 'connected') {{
                        this.stopApiPolling();
                    }}
                }},

                // API轮询降级机制
                apiPollingInterval: null,
                apiPollingEnabled: false,

                startApiPolling: function() {{
                    if (this.apiPollingEnabled) return;

                    console.log('🔄 启动API轮询降级机制');
                    this.apiPollingEnabled = true;

                    this.apiPollingInterval = setInterval(() => {{
                        this.fetchDataViaApi();
                    }}, 10000); // 每10秒轮询一次

                    // 立即执行一次
                    this.fetchDataViaApi();
                }},

                stopApiPolling: function() {{
                    if (!this.apiPollingEnabled) return;

                    console.log('⏹️ 停止API轮询');
                    this.apiPollingEnabled = false;

                    if (this.apiPollingInterval) {{
                        clearInterval(this.apiPollingInterval);
                        this.apiPollingInterval = null;
                    }}
                }},

                fetchDataViaApi: async function() {{
                    try {{
                        // 获取Bug检测统计数据
                        const response = await fetch('/api/v1/bug-detection/statistics');
                        if (response.ok) {{
                            const data = await response.json();

                            // 模拟WebSocket消息格式
                            const mockMessage = {{
                                type: 'stats_update',
                                data: data,
                                source: 'api_polling',
                                timestamp: Date.now() / 1000
                            }};

                            this.handleMessage(mockMessage);
                        }} else {{
                            console.warn('API轮询失败:', response.status);
                        }}
                    }} catch (error) {{
                        console.warn('API轮询错误:', error);
                    }}
                }},
                
                // 添加消息到Streamlit
                addToStreamlitMessages: function(message) {{
                    window.dispatchEvent(new CustomEvent('websocket_message_received', {{
                        detail: message
                    }}));
                }},
                
                // 获取连接状态
                getStatus: function() {{
                    return {{
                        connected: this.connected,
                        readyState: this.socket ? this.socket.readyState : -1,
                        messageQueueLength: this.messageQueue.length,
                        reconnectAttempts: reconnectAttempts
                    }};
                }},
                
                // 断开连接
                disconnect: function() {{
                    if (this.socket) {{
                        this.socket.close();
                    }}
                    this.stopPing();
                    this.stopApiPolling();
                    this.connected = false;
                }}
            }};
            
            // 自动初始化
            if (document.readyState === 'loading') {{
                document.addEventListener('DOMContentLoaded', () => {{
                    window.streamlitWebSocket.init();
                }});
            }} else {{
                window.streamlitWebSocket.init();
            }}
            
            // 页面卸载时断开连接
            window.addEventListener('beforeunload', () => {{
                window.streamlitWebSocket.disconnect();
            }});
            
        }})();
        </script>
        """
        
        # 注入WebSocket客户端脚本
        components.html(websocket_script, height=0)
        
        logger.info("WebSocket客户端已注入到Streamlit页面")
    
    def register_message_handler(self, message_type: str, handler: Callable[[Dict[str, Any]], None]):
        """注册消息处理器"""
        self.message_handlers[message_type] = handler
    
    def get_connection_status(self) -> str:
        """获取连接状态"""
        return st.session_state.get('websocket_connection_status', 'disconnected')
    
    def get_received_messages(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取接收到的消息"""
        messages = st.session_state.get('websocket_messages', [])
        return messages[-limit:] if messages else []
    
    def display_connection_status(self):
        """显示连接状态"""
        status = self.get_connection_status()
        
        if status == 'connected':
            st.success("🔗 WebSocket已连接")
        elif status == 'connecting':
            st.info("🔄 WebSocket连接中...")
        elif status == 'disconnected':
            st.warning("🔌 WebSocket已断开")
        elif status == 'error':
            st.error("❌ WebSocket连接错误")
        elif status == 'failed':
            st.error("💥 WebSocket连接失败")
        else:
            st.info("❓ WebSocket状态未知")
    
    def display_message_log(self, limit: int = 5):
        """显示消息日志"""
        st.subheader("📨 WebSocket消息日志")
        
        messages = self.get_received_messages(limit)
        
        if not messages:
            st.info("暂无WebSocket消息")
            return
        
        for i, message in enumerate(reversed(messages)):
            timestamp = datetime.fromtimestamp(message.get('timestamp', 0))
            message_type = message.get('type', 'unknown')
            
            with st.expander(f"{message_type} - {timestamp.strftime('%H:%M:%S')}", expanded=False):
                st.json(message)
    
    def create_subscription_controls(self):
        """创建订阅控制面板"""
        st.subheader("📡 事件订阅控制")
        
        event_types = [
            'javascript_error',
            'api_performance', 
            'user_behavior',
            'system_health',
            'prediction_accuracy',
            'bug_detected',
            'alert_triggered'
        ]
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**订阅事件类型:**")
            for event_type in event_types:
                if st.checkbox(event_type, key=f"sub_{event_type}"):
                    # 这里可以添加订阅逻辑
                    pass
        
        with col2:
            st.write("**控制操作:**")
            if st.button("🔄 重新连接"):
                # 触发重连
                st.info("正在重新连接WebSocket...")
                st.rerun()
            
            if st.button("📊 获取状态"):
                # 显示详细状态
                st.json({
                    "client_id": self.client_id,
                    "websocket_url": self.websocket_url,
                    "connection_status": self.get_connection_status(),
                    "message_count": len(self.get_received_messages(100))
                })

# 全局WebSocket客户端实例
websocket_client = StreamlitWebSocketClient()

# 便捷函数
def inject_websocket_client(websocket_url: str = "ws://127.0.0.1:8888/ws/bug-detection", log_level: str = 'WARN'):
    """注入WebSocket客户端的便捷函数

    Args:
        websocket_url: WebSocket服务器URL
        log_level: 日志级别 ('DEBUG', 'INFO', 'WARN', 'ERROR', 'SILENT')
    """
    client = StreamlitWebSocketClient(websocket_url, log_level)
    client.inject_websocket_client()
    return client

def display_websocket_status():
    """显示WebSocket状态的便捷函数"""
    websocket_client.display_connection_status()

def display_websocket_messages(limit: int = 5):
    """显示WebSocket消息的便捷函数"""
    websocket_client.display_message_log(limit)

if __name__ == "__main__":
    # 测试代码
    st.title("WebSocket客户端测试")
    
    # 注入WebSocket客户端
    client = inject_websocket_client()
    
    # 显示状态和控制面板
    client.display_connection_status()
    client.create_subscription_controls()
    client.display_message_log()
