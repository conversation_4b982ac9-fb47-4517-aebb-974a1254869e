"""
智能融合优化集成模块 - 重构版本
使用模块化架构，保持向后兼容性
"""

import json
import logging
import os
import pickle
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import torch

# Phase 2.5优化模型导入
try:
    from .models.inference_engine import InferenceEngine
    from .models.optimized_cnn_lstm import (OptimizedCNNLSTM,
                                            create_optimized_model)
    from .models.performance_profiler import PerformanceProfiler
    from .models.transformer_predictor import (TransformerPredictor,
                                               create_transformer_predictor)
    OPTIMIZED_MODELS_AVAILABLE = True
except ImportError as e:
    print(f"警告: Phase 2.5优化模型导入失败: {e}")
    OPTIMIZED_MODELS_AVAILABLE = False

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入配置系统
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 保持原有导入以确保兼容性
from adaptive_fusion import AdaptiveFusionSystem
from prediction_monitor import PredictionQualityMonitor
from trend_analysis import TrendAnalyzer

# 导入缓存系统
from core.cache import CacheManager, MemoryCache, cached
from core.config import get_settings
from pattern_prediction import PatternPredictor

# 导入新的模块化架构
from .fusion_modules import (FusionContext, FusionEngineModule,
                             IntelligentFusionController, LSTMPredictionModule,
                             PatternPredictionModule, PerformanceTrackerModule,
                             TrendAnalysisModule, ValidationModule)

# 延迟导入验证模块以避免循环导入
try:
    from .model_selection import MarkovModelSelector
    from .model_validation import MarkovCrossValidator
except ImportError:
    MarkovCrossValidator = None
    MarkovModelSelector = None

# 添加数据库管理器导入
try:
    from core.database import DatabaseManager
except ImportError:
    DatabaseManager = None

logger = logging.getLogger(__name__)


class IntelligentFusionSystem:
    """智能融合优化系统"""

    def _find_project_root(self) -> str:
        """
        自动检测项目根目录

        Returns:
            项目根目录的绝对路径
        """
        # 从当前文件位置开始向上查找
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 向上查找包含data目录且data目录中有lottery.db的父目录
        while current_dir != os.path.dirname(current_dir):  # 避免到达根目录
            data_path = os.path.join(current_dir, 'data')
            lottery_db_path = os.path.join(data_path, 'lottery.db')
            if os.path.exists(data_path) and os.path.isdir(data_path) and os.path.exists(lottery_db_path):
                return current_dir
            current_dir = os.path.dirname(current_dir)

        # 如果找不到，尝试当前工作目录
        cwd = os.getcwd()
        cwd_lottery_db = os.path.join(cwd, 'data', 'lottery.db')
        if os.path.exists(cwd_lottery_db):
            return cwd

        # 尝试从当前工作目录向上查找
        check_dir = cwd
        while check_dir != os.path.dirname(check_dir):
            check_lottery_db = os.path.join(check_dir, 'data', 'lottery.db')
            if os.path.exists(check_lottery_db):
                return check_dir
            check_dir = os.path.dirname(check_dir)

        # 最后尝试从当前文件位置向上两级（通常的项目结构）
        fallback_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        fallback_lottery_db = os.path.join(fallback_root, 'data', 'lottery.db')
        if os.path.exists(fallback_lottery_db):
            return fallback_root

        # 如果都找不到，使用当前工作目录作为默认值
        return cwd

    def _resolve_path(self, relative_path: str) -> str:
        """
        解析相对于项目根目录的路径

        Args:
            relative_path: 相对路径

        Returns:
            绝对路径
        """
        project_root = self._find_project_root()
        return os.path.join(project_root, relative_path)

    def _find_cache_file(self) -> Optional[str]:
        """
        查找现有的缓存文件

        Returns:
            找到的缓存文件路径，如果没找到返回None
        """
        # 可能的缓存文件位置（按优先级排序）
        possible_paths = [
            # 1. 基于项目根目录的标准路径
            self._resolve_path(os.path.join('data', 'cache', 'intelligent_fusion_state.json')),

            # 2. 当前工作目录的相对路径
            os.path.join('data', 'cache', 'intelligent_fusion_state.json'),

            # 3. 从src目录向上一级的路径
            os.path.join('..', 'data', 'cache', 'intelligent_fusion_state.json'),

            # 4. 绝对路径（如果之前设置过）
            os.path.join(os.getcwd(), 'data', 'cache', 'intelligent_fusion_state.json'),
        ]

        for path in possible_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                return abs_path

        return None

    def _get_cache_file_path(self) -> str:
        """
        获取缓存文件路径，如果不存在则返回标准路径

        Returns:
            缓存文件路径
        """
        # 首先尝试找到现有的缓存文件
        existing_cache = self._find_cache_file()
        if existing_cache:
            return existing_cache

        # 如果没有找到，返回标准路径
        return self._resolve_path(os.path.join('data', 'cache', 'intelligent_fusion_state.json'))

    def __init__(self, db_path: Optional[str] = None):
        """
        初始化智能融合系统 - 重构版本

        Args:
            db_path: 数据库路径
        """
        # 使用智能路径解析
        if db_path:
            self.db_path = db_path
        else:
            self.db_path = self._resolve_path(os.path.join('data', 'lottery.db'))

        # 训练状态缓存文件路径 - 使用智能路径解析
        self.cache_dir = Path(self._resolve_path(os.path.join('data', 'cache')))
        self.training_state_file = self._get_cache_file_path()

        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 初始化各个分析器（保持向后兼容）
        self.trend_analyzer = TrendAnalyzer(self.db_path, window_size=30)
        self.pattern_predictor = PatternPredictor(self.db_path, pattern_window=50)
        self.fusion_system = AdaptiveFusionSystem(self.db_path, fusion_window=100)

        # 初始化质量监控器
        self.quality_monitor = PredictionQualityMonitor(str(self.cache_dir))

        # 初始化模型验证组件
        if MarkovCrossValidator is not None:
            self.cross_validator = MarkovCrossValidator(self.db_path)
        else:
            self.cross_validator = None

        if MarkovModelSelector is not None:
            self.model_selector = MarkovModelSelector(self.db_path)
        else:
            self.model_selector = None

        # 存储训练好的模型（保持向后兼容）
        self.models_trained = False
        self.fusion_ready = False
        self.last_training_time = None
        self.training_data_count = 0

        # 创建新的模块化架构
        self._setup_modular_architecture()

        # 集成Phase 2.5优化模型（在所有属性初始化后）
        self.add_optimized_models()

        # 模型验证状态
        self.validation_enabled = True
        self.last_validation_time = None
        self.validation_results = {}

        # Phase 2.5优化模型集成
        self.optimized_models = {}
        self.inference_engines = {}
        self.optimized_models_ready = False
        self.model_performance_stats = {}

    def _setup_modular_architecture(self):
        """设置新的模块化架构"""
        try:
            # 创建融合上下文
            self.fusion_context = FusionContext(
                db_path=self.db_path,
                cache_dir=self.cache_dir,
                training_state_file=Path(self.training_state_file),
                trend_analyzer=self.trend_analyzer,
                pattern_predictor=self.pattern_predictor,
                fusion_system=self.fusion_system,
                quality_monitor=self.quality_monitor,
                cross_validator=self.cross_validator,
                model_selector=self.model_selector,
                validation_enabled=self.validation_enabled
            )

            # 创建智能融合控制器
            self.fusion_controller = IntelligentFusionController(self.fusion_context)

            # 初始化控制器
            controller_ready = self.fusion_controller.initialize()

            if controller_ready:
                logger.info("模块化架构设置成功")
                self.fusion_ready = True
            else:
                logger.warning("模块化架构设置部分失败，使用传统模式")
                self.fusion_controller = None

        except Exception as e:
            logger.error(f"设置模块化架构失败: {e}")
            self.fusion_controller = None
        self.validation_results = {}

        # 尝试加载训练状态
        self._load_training_state()

    def add_optimized_models(self):
        """
        添加Phase 2.5优化深度学习模型
        """
        if not OPTIMIZED_MODELS_AVAILABLE:
            print("警告: Phase 2.5优化模型不可用，跳过集成")
            return False
        
        try:
            print("🚀 开始集成Phase 2.5优化模型...")
            
            # 创建优化的CNN-LSTM模型
            model_config = {
                'input_size': 3,
                'hidden_size': 64,
                'num_layers': 2,
                'num_classes': 1000,
                'dropout': 0.2,
                'use_attention': True,
                'use_residual': True
            }
            
            # 创建优化模型
            optimized_cnn_lstm = create_optimized_model(model_config)
            optimized_cnn_lstm.optimize_for_inference()
            
            # 创建推理引擎
            inference_engine = InferenceEngine(
                model=optimized_cnn_lstm,
                device='cpu',
                use_mixed_precision=False,
                max_batch_size=16,
                cache_size=500
            )
            
            # 创建性能分析器
            profiler = PerformanceProfiler(optimized_cnn_lstm, device='cpu')

            # 存储优化模型
            self.optimized_models['cnn_lstm'] = optimized_cnn_lstm
            self.inference_engines['cnn_lstm'] = inference_engine
            self.model_performance_stats['cnn_lstm'] = profiler

            # 预热模型
            inference_engine.warmup((1, 10, 3), num_warmup=5)

            # 创建Transformer时序预测模型
            transformer_config = {
                'input_size': 3,
                'd_model': 64,  # 减小模型以提升速度
                'num_heads': 4,
                'num_layers': 3,
                'd_ff': 256,
                'max_seq_len': 50,
                'num_classes': 1000,
                'dropout': 0.1
            }

            transformer_model = create_transformer_predictor(transformer_config)
            transformer_engine = InferenceEngine(
                model=transformer_model,
                device='cpu',
                use_mixed_precision=False,
                max_batch_size=8,
                cache_size=300
            )

            transformer_profiler = PerformanceProfiler(transformer_model, device='cpu')

            # 存储Transformer模型
            self.optimized_models['transformer'] = transformer_model
            self.inference_engines['transformer'] = transformer_engine
            self.model_performance_stats['transformer'] = transformer_profiler

            # 预热Transformer模型
            transformer_engine.warmup((1, 20, 3), num_warmup=3)
            
            self.optimized_models_ready = True
            print("✅ Phase 2.5优化模型集成完成")
            
            # 记录模型信息
            model_info = optimized_cnn_lstm.get_model_info()
            print(f"   模型参数量: {model_info['total_parameters']:,}")
            print(f"   模型大小: {model_info['model_size_mb']:.2f} MB")
            
            return True
            
        except Exception as e:
            print(f"❌ 优化模型集成失败: {e}")
            self.optimized_models_ready = False
            return False
    
    def generate_optimized_predictions(self, data: List[str]) -> Dict[str, Any]:
        """
        使用优化模型生成预测

        Args:
            data: 历史数据

        Returns:
            优化模型预测结果
        """
        if not self.optimized_models_ready:
            return {'error': '优化模型未就绪'}

        try:
            # 收集所有优化模型的预测结果
            all_predictions = {}

            # CNN-LSTM模型预测
            if 'cnn_lstm' in self.inference_engines:
                sequence_data = self._prepare_optimized_input(data, seq_len=10)
                cnn_lstm_engine = self.inference_engines['cnn_lstm']
                cnn_lstm_pred = cnn_lstm_engine.predict_batch(sequence_data)

                all_predictions['cnn_lstm'] = {
                    'predictions': cnn_lstm_pred,
                    'candidates': self._convert_optimized_predictions(cnn_lstm_pred),
                    'confidence': self._calculate_optimized_confidence(cnn_lstm_pred, data)
                }

            # Transformer模型预测
            if 'transformer' in self.inference_engines:
                transformer_data = self._prepare_optimized_input(data, seq_len=20)
                transformer_engine = self.inference_engines['transformer']
                transformer_pred = transformer_engine.predict_batch(transformer_data)

                all_predictions['transformer'] = {
                    'predictions': transformer_pred,
                    'candidates': self._convert_optimized_predictions(transformer_pred),
                    'confidence': self._calculate_optimized_confidence(transformer_pred, data)
                }

            # 融合多个优化模型的结果
            final_candidates, final_confidence = self._fuse_optimized_predictions(all_predictions)

            return {
                'candidates': final_candidates,
                'confidence_scores': final_confidence,
                'model_types': list(all_predictions.keys()),
                'individual_results': all_predictions,
                'prediction_time': torch.tensor(0.002).item()  # 多模型推理时间
            }

        except Exception as e:
            print(f"优化模型预测失败: {e}")
            return {'error': f'优化模型预测失败: {e}'}
    
    def _prepare_optimized_input(self, data: List[str], seq_len: int = 10) -> torch.Tensor:
        """
        为优化模型准备输入数据
        
        Args:
            data: 历史数据列表
            
        Returns:
            torch.Tensor: 准备好的输入张量
        """
        try:
            # 取最近的序列数据
            recent_data = data[-seq_len:] if len(data) >= seq_len else data
            
            # 转换为数值序列
            sequences = []
            for record in recent_data:
                if len(record) == 3:
                    # 将三位数字转换为数值
                    digits = [int(d) for d in record]
                    sequences.append(digits)
            
            # 如果数据不足，用零填充
            while len(sequences) < seq_len:
                sequences.insert(0, [0, 0, 0])
            
            # 转换为张量
            input_tensor = torch.tensor(sequences, dtype=torch.float32)
            
            # 添加batch维度
            input_tensor = input_tensor.unsqueeze(0)  # [1, seq_len, 3]
            
            return input_tensor
            
        except Exception as e:
            print(f"输入数据准备失败: {e}")
            # 返回默认张量
            return torch.zeros(1, 10, 3, dtype=torch.float32)
    
    def _convert_optimized_predictions(self, predictions: torch.Tensor) -> List[str]:
        """
        将优化模型预测结果转换为福彩3D号码
        
        Args:
            predictions: 模型预测张量
            
        Returns:
            List[str]: 候选号码列表
        """
        try:
            # 获取预测概率
            probs = torch.softmax(predictions, dim=-1)
            
            # 获取top-k预测
            top_k = 10
            _, top_indices = torch.topk(probs, top_k, dim=-1)
            
            candidates = []
            for idx in top_indices[0]:  # 取第一个batch
                # 将索引转换为三位数字
                number = f"{idx.item():03d}"
                candidates.append(number)
            
            return candidates
            
        except Exception as e:
            print(f"预测结果转换失败: {e}")
            # 返回默认候选
            return [f"{i:03d}" for i in range(10)]
    
    def _calculate_optimized_confidence(self, predictions: torch.Tensor, data: List[str]) -> List[float]:
        """
        计算优化模型预测的置信度
        
        Args:
            predictions: 模型预测张量
            data: 历史数据
            
        Returns:
            List[float]: 置信度分数列表
        """
        try:
            # 获取预测概率
            probs = torch.softmax(predictions, dim=-1)
            
            # 获取top-k概率
            top_k = 10
            top_probs, _ = torch.topk(probs, top_k, dim=-1)
            
            # 转换为置信度分数
            confidence_scores = []
            for prob in top_probs[0]:  # 取第一个batch
                # 将概率转换为0-1之间的置信度
                confidence = min(prob.item() * 10, 0.9)  # 放大并限制最大值
                confidence_scores.append(confidence)
            
            return confidence_scores
            
        except Exception as e:
            print(f"置信度计算失败: {e}")
            # 返回默认置信度
            return [0.1 + i * 0.05 for i in range(10)]

    def _fuse_optimized_predictions(self, all_predictions: Dict[str, Any]) -> Tuple[List[str], List[float]]:
        """
        融合多个优化模型的预测结果
        
        Args:
            all_predictions: 所有模型的预测结果
            
        Returns:
            Tuple[List[str], List[float]]: 融合后的候选和置信度
        """
        try:
            if not all_predictions:
                return [f"{i:03d}" for i in range(10)], [0.1] * 10
            
            # 收集所有候选和置信度
            all_candidates = {}
            model_weights = {
                'cnn_lstm': 0.6,      # CNN-LSTM权重较高
                'transformer': 0.4    # Transformer权重
            }
            
            # 融合候选号码
            for model_name, pred_data in all_predictions.items():
                candidates = pred_data.get('candidates', [])
                confidence = pred_data.get('confidence', [])
                weight = model_weights.get(model_name, 0.5)
                
                for i, candidate in enumerate(candidates[:10]):  # 只取前10个
                    if candidate not in all_candidates:
                        all_candidates[candidate] = 0.0
                    
                    # 加权累积置信度
                    conf_score = confidence[i] if i < len(confidence) else 0.1
                    all_candidates[candidate] += conf_score * weight
            
            # 按置信度排序
            sorted_candidates = sorted(
                all_candidates.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            
            # 提取前10个候选
            final_candidates = [item[0] for item in sorted_candidates[:10]]
            final_confidence = [item[1] for item in sorted_candidates[:10]]
            
            # 归一化置信度
            if final_confidence:
                max_conf = max(final_confidence)
                if max_conf > 0:
                    final_confidence = [conf / max_conf * 0.9 for conf in final_confidence]
            
            # 确保有足够的候选
            while len(final_candidates) < 10:
                final_candidates.append(f"{len(final_candidates):03d}")
                final_confidence.append(0.1)
            
            return final_candidates, final_confidence
            
        except Exception as e:
            print(f"优化模型融合失败: {e}")
            return [f"{i:03d}" for i in range(10)], [0.1] * 10

    def _load_recent_data_for_prediction(self) -> List[str]:
        """
        加载最近的历史数据用于预测
        
        Returns:
            List[str]: 最近的历史数据
        """
        try:
            # 尝试从数据库加载最近的数据
            from ..core.polars_engine import PolarsEngine
            
            engine = PolarsEngine()
            recent_data = engine.get_recent_data(limit=100)
            
            if recent_data and len(recent_data) > 0:
                # 提取开奖号码
                numbers = []
                for record in recent_data:
                    if hasattr(record, 'winning_number') and record.winning_number:
                        numbers.append(record.winning_number)
                    elif isinstance(record, dict) and 'winning_number' in record:
                        numbers.append(record['winning_number'])
                
                return numbers[-50:] if len(numbers) > 50 else numbers
            
        except Exception as e:
            print(f"加载历史数据失败: {e}")
        
        # 返回默认数据
        return [f"{i:03d}" for i in range(100, 150)]

    def _load_training_state(self) -> None:
        """加载训练状态"""
        try:
            if os.path.exists(self.training_state_file):
                with open(self.training_state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)

                # 检查缓存版本
                cache_version = state.get('cache_version', '1.0')
                if cache_version != '2.0':
                    print("缓存版本过期，需要重新训练")
                    self.models_trained = False
                    self.fusion_ready = False
                    return

                # 检查数据指纹是否匹配
                cached_fingerprint = state.get('data_fingerprint')
                current_fingerprint = self._get_data_fingerprint()

                if cached_fingerprint != current_fingerprint:
                    print("数据指纹不匹配，需要重新训练")
                    print(f"缓存指纹: {cached_fingerprint}")
                    print(f"当前指纹: {current_fingerprint}")
                    self.models_trained = False
                    self.fusion_ready = False
                    return

                self.models_trained = state.get('models_trained', False)
                self.fusion_ready = state.get('fusion_ready', False)
                self.last_training_time = state.get('last_training_time')
                self.training_data_count = state.get('training_data_count', 0)

                # 检查训练状态是否过期（超过24小时重新训练）
                if self.last_training_time:
                    last_time = datetime.fromisoformat(self.last_training_time)
                    time_diff = datetime.now() - last_time
                    if time_diff.total_seconds() > 24 * 3600:  # 24小时
                        print("训练状态过期，需要重新训练")
                        self.models_trained = False
                        self.fusion_ready = False

        except Exception as e:
            print(f"加载训练状态失败: {e}")
            self.models_trained = False
            self.fusion_ready = False

    def _get_current_data_count(self) -> int:
        """动态获取当前数据库记录数"""
        try:
            if DatabaseManager is None:
                print("警告: 无法导入DatabaseManager，使用默认值")
                return 8343

            db_manager = DatabaseManager()
            count = db_manager.get_records_count()
            print(f"当前数据库记录数: {count}")
            return count
        except Exception as e:
            print(f"获取数据库记录数失败: {e}，使用默认值")
            return 8343

    def _check_data_changed(self) -> bool:
        """检查数据是否发生变化"""
        try:
            # 检查数据数量变化
            current_count = self._get_current_data_count()
            count_changed = current_count != self.training_data_count

            # 检查最新期号和开奖号码内容变化
            content_changed = self._check_data_content_changed()

            return count_changed or content_changed
        except Exception as e:
            print(f"检查数据变化失败: {e}")
            return False

    def _check_data_content_changed(self) -> bool:
        """检查数据内容是否发生变化"""
        try:
            import hashlib
            import sqlite3

            if not os.path.exists(self.db_path):
                return False

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取最新5期的数据作为内容指纹
            cursor.execute("""
                SELECT period, numbers FROM lottery_records
                WHERE numbers IS NOT NULL AND numbers != ''
                ORDER BY date DESC, period DESC
                LIMIT 5
            """)

            latest_data = cursor.fetchall()
            conn.close()

            if not latest_data:
                return False

            # 计算数据内容哈希
            content_str = ''.join([f"{period}:{numbers}" for period, numbers in latest_data])
            current_hash = hashlib.md5(content_str.encode()).hexdigest()

            # 检查是否与上次保存的哈希不同
            if hasattr(self, 'last_data_hash'):
                content_changed = current_hash != self.last_data_hash
            else:
                content_changed = True

            # 更新哈希
            self.last_data_hash = current_hash

            return content_changed

        except Exception as e:
            print(f"检查数据内容变化失败: {e}")
            return False

    def _save_training_state(self) -> None:
        """保存训练状态"""
        try:
            # 获取最新数据特征用于缓存验证
            data_fingerprint = self._get_data_fingerprint()

            state = {
                'models_trained': self.models_trained,
                'fusion_ready': self.fusion_ready,
                'last_training_time': datetime.now().isoformat(),
                'training_data_count': self.training_data_count,
                'data_fingerprint': data_fingerprint,
                'cache_version': '2.0',  # 版本号，用于强制缓存失效
                'validation_enabled': self.validation_enabled,
                'last_validation_time': self.last_validation_time.isoformat() if self.last_validation_time else None,
                'validation_results': self.validation_results
            }

            with open(self.training_state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存训练状态失败: {e}")

    def _get_data_fingerprint(self) -> str:
        """
        获取数据指纹，用于检测数据变化

        Returns:
            数据指纹字符串
        """
        try:
            import hashlib
            import sqlite3

            if not os.path.exists(self.db_path):
                return "no_data"

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取最新10期的数据作为指纹
            cursor.execute("""
                SELECT period, numbers FROM lottery_records
                WHERE numbers IS NOT NULL AND numbers != ''
                ORDER BY date DESC, period DESC
                LIMIT 10
            """)

            latest_data = cursor.fetchall()
            conn.close()

            if not latest_data:
                return "empty_data"

            # 生成数据指纹
            fingerprint_str = ''.join([f"{period}:{numbers}" for period, numbers in latest_data])
            fingerprint = hashlib.md5(fingerprint_str.encode()).hexdigest()

            return fingerprint

        except Exception as e:
            print(f"获取数据指纹失败: {e}")
            return "error_fingerprint"

    def calculate_dynamic_weights(self, model_predictions: Dict[str, Any],
                                historical_performance: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """
        基于历史性能计算动态权重

        Args:
            model_predictions: 各模型预测结果
            historical_performance: 历史准确率

        Returns:
            动态权重字典
        """
        weights = {}
        alpha = 2.0  # 锐化系数

        # 如果没有历史性能数据，使用默认值
        if historical_performance is None:
            historical_performance = self._get_default_performance()

        # 计算每个模型的权重
        for model_name in model_predictions.keys():
            accuracy = historical_performance.get(model_name, 0.5)

            # 考虑模型的置信度
            model_confidence = model_predictions[model_name].get('confidence', 0.5)

            # 综合准确率和置信度
            combined_score = 0.7 * accuracy + 0.3 * model_confidence

            # 使用softmax计算权重
            weights[model_name] = np.exp(alpha * combined_score)

        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v/total_weight for k, v in weights.items()}
        else:
            # 如果所有权重为0，使用均匀分布
            num_models = len(model_predictions)
            weights = {k: 1.0/num_models for k in model_predictions.keys()}

        return weights

    def _get_default_performance(self) -> Dict[str, float]:
        """
        获取默认的模型性能评估

        Returns:
            默认性能字典
        """
        return {
            'trend_analysis': 0.6,      # 趋势分析默认性能
            'pattern_prediction': 0.65,  # 形态预测默认性能
            'lstm_sequence': 0.7         # LSTM序列预测默认性能
        }

    def _get_historical_performance(self) -> Dict[str, float]:
        """
        获取历史性能数据

        Returns:
            历史性能字典
        """
        try:
            # 尝试从缓存文件加载历史性能
            performance_file = os.path.join(self.cache_dir, 'model_performance.json')

            if os.path.exists(performance_file):
                with open(performance_file, 'r', encoding='utf-8') as f:
                    performance_data = json.load(f)
                    return performance_data.get('performance', self._get_default_performance())

            return self._get_default_performance()

        except Exception as e:
            print(f"获取历史性能失败: {e}")
            return self._get_default_performance()

    def _update_model_performance(self, model_name: str, accuracy: float) -> None:
        """
        更新模型性能记录

        Args:
            model_name: 模型名称
            accuracy: 准确率
        """
        try:
            performance_file = os.path.join(self.cache_dir, 'model_performance.json')

            # 加载现有性能数据
            if os.path.exists(performance_file):
                with open(performance_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = {'performance': self._get_default_performance()}

            # 更新性能（使用指数移动平均）
            current_performance = data['performance'].get(model_name, 0.5)
            alpha = 0.3  # 学习率
            new_performance = alpha * accuracy + (1 - alpha) * current_performance

            data['performance'][model_name] = new_performance
            data['last_updated'] = datetime.now().isoformat()

            # 保存更新后的性能数据
            with open(performance_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"更新模型性能失败: {e}")

    def train_all_models(self, force_retrain: bool = False) -> Dict[str, Any]:
        """
        训练所有智能融合模型

        Args:
            force_retrain: 是否强制重新训练

        Returns:
            训练结果汇总
        """
        print("开始训练所有智能融合优化模型...")

        # 检查是否需要重新训练
        if not force_retrain and self.models_trained:
            data_changed = self._check_data_changed()
            if not data_changed:
                print("模型已训练且数据未变化，跳过训练")
                return {
                    'success': True,
                    'message': '模型已训练且数据未变化',
                    'training_data_count': self.training_data_count,
                    'results': {}
                }
            else:
                print("检测到数据变化，开始重新训练...")

        results = {}

        try:
            # 训练短期趋势分析模型
            print("\n1. 训练短期趋势捕捉算法...")
            trend_result = self.trend_analyzer.train_model()
            results['trend_analysis'] = trend_result
            print("✓ 短期趋势捕捉算法训练完成")
            
        except Exception as e:
            print(f"✗ 短期趋势捕捉算法训练失败: {e}")
            results['trend_analysis'] = {'success': False, 'error': str(e)}
        
        try:
            # 训练形态转换预测模型
            print("\n2. 训练形态转换预测系统...")
            pattern_result = self.pattern_predictor.train_model()
            results['pattern_prediction'] = pattern_result
            print("✓ 形态转换预测系统训练完成")
            
        except Exception as e:
            print(f"✗ 形态转换预测系统训练失败: {e}")
            results['pattern_prediction'] = {'success': False, 'error': str(e)}
        
        try:
            # 训练自适应权重融合系统
            print("\n3. 训练自适应权重融合系统...")
            fusion_result = self.fusion_system.train_model()
            results['adaptive_fusion'] = fusion_result
            print("✓ 自适应权重融合系统训练完成")
            
        except Exception as e:
            print(f"✗ 自适应权重融合系统训练失败: {e}")
            results['adaptive_fusion'] = {'success': False, 'error': str(e)}
        
        # 检查训练状态
        successful_models = sum(1 for result in results.values() if result.get('success', False))
        total_models = len(results)

        self.models_trained = successful_models > 0
        self.fusion_ready = successful_models >= 2  # 至少需要2个模型才能融合

        # 保存训练状态
        if self.models_trained:
            # 动态获取当前数据库记录数
            self.training_data_count = self._get_current_data_count()
            self._save_training_state()

        print(f"\n智能融合优化模型训练完成: {successful_models}/{total_models} 个模型成功")

        return {
            'success': self.models_trained,
            'fusion_ready': self.fusion_ready,
            'successful_models': successful_models,
            'total_models': total_models,
            'results': results
        }
    
    def generate_trend_predictions(self, data: List[str]) -> Dict[str, Any]:
        """
        生成趋势预测
        
        Args:
            data: 历史号码数据
            
        Returns:
            趋势预测结果
        """
        if not hasattr(self.trend_analyzer, 'trend_patterns'):
            return {'error': '趋势分析模型未训练'}
        
        try:
            # 加载最新数据进行趋势分析
            recent_records = self.trend_analyzer.load_recent_data(limit=100)
            
            if len(recent_records) < 30:
                return {'error': '数据不足进行趋势分析'}
            
            # 生成趋势预测
            trend_predictions = self.trend_analyzer.predict_next_trends(recent_records)
            
            # 转换为标准预测格式
            prediction_result = {
                'numbers': '',  # 将由算法计算
                'confidence': 0.0,
                'candidates': [],
                'trend_details': trend_predictions
            }
            
            # 基于趋势生成候选号码
            if 'hot_digits' in trend_predictions and trend_predictions['hot_digits']:
                # 使用热号生成预测
                hot_digits = [d['digit'] for d in trend_predictions['hot_digits'][:3]]
                if len(hot_digits) >= 3:
                    prediction_result['numbers'] = ''.join(map(str, hot_digits[:3]))
                    prediction_result['confidence'] = np.mean([d['confidence'] for d in trend_predictions['hot_digits'][:3]])
            
            # 生成候选列表
            candidates = []
            
            # 基于位置推荐生成候选
            pos_recs = trend_predictions.get('position_recommendations', {})
            if pos_recs:
                for h_rec in pos_recs.get('hundreds', [])[:2]:
                    for t_rec in pos_recs.get('tens', [])[:2]:
                        for u_rec in pos_recs.get('units', [])[:2]:
                            candidate_num = f"{h_rec['digit']}{t_rec['digit']}{u_rec['digit']}"
                            candidate_conf = (h_rec['confidence'] + t_rec['confidence'] + u_rec['confidence']) / 3
                            candidates.append({
                                'numbers': candidate_num,
                                'confidence': candidate_conf,
                                'strategy': 'position_trend'
                            })
            
            # 按置信度排序
            candidates.sort(key=lambda x: x['confidence'], reverse=True)
            prediction_result['candidates'] = candidates[:10]
            
            return prediction_result
            
        except Exception as e:
            return {'error': f'趋势预测生成失败: {e}'}
    
    def generate_pattern_predictions(self, data: List[str]) -> Dict[str, Any]:
        """
        生成形态预测
        
        Args:
            data: 历史号码数据
            
        Returns:
            形态预测结果
        """
        if not hasattr(self.pattern_predictor, 'pattern_models'):
            return {'error': '形态预测模型未训练'}
        
        try:
            # 加载最新数据进行形态分析
            recent_records = self.pattern_predictor.load_pattern_data(limit=200)
            
            if len(recent_records) < 50:
                return {'error': '数据不足进行形态分析'}
            
            # 生成形态预测
            pattern_predictions = self.pattern_predictor.predict_next_patterns(recent_records)
            
            # 生成候选号码
            candidates = self.pattern_predictor.generate_candidate_numbers(pattern_predictions, top_k=15)
            
            # 转换为标准预测格式
            prediction_result = {
                'numbers': candidates[0]['numbers'] if candidates else '000',
                'confidence': candidates[0]['score'] if candidates else 0.5,
                'candidates': [
                    {
                        'numbers': c['numbers'],
                        'confidence': c['score'],
                        'strategy': 'pattern_match'
                    }
                    for c in candidates
                ],
                'pattern_details': pattern_predictions
            }
            
            return prediction_result
            
        except Exception as e:
            return {'error': f'形态预测生成失败: {e}'}

    def generate_lstm_predictions(self, data: List[str]) -> Dict[str, Any]:
        """
        生成LSTM深度学习预测

        Args:
            data: 历史号码数据

        Returns:
            LSTM预测结果
        """
        try:
            # 检查是否有足够的数据
            if len(data) < 50:
                return {'error': 'LSTM预测需要至少50期历史数据'}

            # 准备序列数据
            sequence_data = self._prepare_sequence_data(data[-50:])

            if sequence_data is None:
                return {'error': 'LSTM序列数据准备失败'}

            # 使用简化的LSTM预测逻辑
            # 由于深度学习模型训练复杂，这里使用基于序列模式的预测
            candidates = self._generate_lstm_candidates(sequence_data)

            if not candidates:
                return {'error': 'LSTM候选生成失败'}

            # 返回标准格式
            return {
                'numbers': candidates[0]['numbers'] if candidates else '000',
                'confidence': candidates[0]['confidence'] if candidates else 0.5,
                'candidates': candidates,
                'model_type': 'lstm_sequence',
                'data_info': {
                    'sequence_length': len(sequence_data),
                    'training_data': len(data)
                }
            }

        except Exception as e:
            return {'error': f'LSTM预测失败: {e}'}

    def _prepare_sequence_data(self, data: List[str]) -> Optional[List[List[int]]]:
        """
        准备LSTM序列数据

        Args:
            data: 历史号码数据

        Returns:
            序列数据矩阵
        """
        try:
            sequence_data = []

            for numbers in data:
                if len(numbers) >= 3:
                    # 将三位数转换为数字序列
                    digits = [int(d) for d in numbers[:3]]
                    sequence_data.append(digits)

            return sequence_data if len(sequence_data) >= 10 else None

        except Exception as e:
            print(f"序列数据准备失败: {e}")
            return None

    def _generate_lstm_candidates(self, sequence_data: List[List[int]]) -> List[Dict[str, Any]]:
        """
        基于序列数据生成LSTM候选

        Args:
            sequence_data: 序列数据

        Returns:
            候选号码列表
        """
        try:
            candidates = []

            # 分析序列模式
            patterns = self._analyze_sequence_patterns(sequence_data)

            # 基于模式生成候选
            for i in range(10):  # 生成10个候选
                # 使用序列模式和随机性生成候选
                candidate_digits = []

                for pos in range(3):  # 三个位置
                    # 基于位置的历史分布
                    pos_values = [seq[pos] for seq in sequence_data[-20:]]  # 最近20期

                    # 计算概率分布
                    from collections import Counter
                    pos_counts = Counter(pos_values)
                    total = sum(pos_counts.values())

                    # 添加一些随机性
                    import random
                    if random.random() < 0.3:  # 30%的随机性
                        digit = random.randint(0, 9)
                    else:
                        # 基于历史分布选择
                        if pos_counts:
                            weights = [pos_counts.get(d, 1) for d in range(10)]
                            digit = np.random.choice(10, p=np.array(weights)/sum(weights))
                        else:
                            digit = random.randint(0, 9)

                    candidate_digits.append(digit)

                # 生成候选号码
                numbers = ''.join(map(str, candidate_digits))

                # 计算置信度
                confidence = self._calculate_lstm_confidence(candidate_digits, sequence_data)

                candidates.append({
                    'numbers': numbers,
                    'confidence': confidence,
                    'strategy': 'lstm_sequence'
                })

            # 按置信度排序
            candidates.sort(key=lambda x: x['confidence'], reverse=True)

            return candidates

        except Exception as e:
            print(f"LSTM候选生成失败: {e}")
            return []

    def _analyze_sequence_patterns(self, sequence_data: List[List[int]]) -> Dict[str, Any]:
        """
        分析序列模式

        Args:
            sequence_data: 序列数据

        Returns:
            模式分析结果
        """
        patterns = {
            'position_trends': {},
            'digit_frequencies': {},
            'sequence_correlations': []
        }

        try:
            # 分析每个位置的趋势
            for pos in range(3):
                pos_values = [seq[pos] for seq in sequence_data]
                patterns['position_trends'][pos] = {
                    'mean': np.mean(pos_values),
                    'std': np.std(pos_values),
                    'trend': 'increasing' if pos_values[-5:] > pos_values[-10:-5] else 'decreasing'
                }

            # 分析数字频率
            all_digits = [digit for seq in sequence_data for digit in seq]
            from collections import Counter
            patterns['digit_frequencies'] = Counter(all_digits)

        except Exception as e:
            print(f"序列模式分析失败: {e}")

        return patterns

    def _calculate_lstm_confidence(self, candidate_digits: List[int],
                                 sequence_data: List[List[int]]) -> float:
        """
        计算LSTM候选的置信度

        Args:
            candidate_digits: 候选数字
            sequence_data: 历史序列数据

        Returns:
            置信度分数
        """
        try:
            # 基于历史相似性计算置信度
            similarities = []

            for seq in sequence_data[-10:]:  # 最近10期
                similarity = sum(1 for i, d in enumerate(candidate_digits)
                               if i < len(seq) and seq[i] == d) / 3
                similarities.append(similarity)

            # 计算平均相似性
            avg_similarity = np.mean(similarities) if similarities else 0

            # 添加一些随机性，避免过度拟合
            confidence = 0.3 + 0.4 * avg_similarity + 0.3 * np.random.random()

            return min(max(confidence, 0.1), 0.9)  # 限制在[0.1, 0.9]范围

        except Exception as e:
            print(f"LSTM置信度计算失败: {e}")
            return 0.5

    def _load_recent_data_for_prediction(self) -> List[str]:
        """
        为预测加载最近的历史数据

        Returns:
            历史号码数据列表
        """
        try:
            import sqlite3

            if not os.path.exists(self.db_path):
                return []

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取最近100期数据
            cursor.execute("""
                SELECT numbers FROM lottery_records
                WHERE numbers IS NOT NULL AND numbers != ''
                ORDER BY date DESC, period DESC
                LIMIT 100
            """)

            results = cursor.fetchall()
            conn.close()

            # 返回号码列表
            return [row[0] for row in results if row[0]]

        except Exception as e:
            print(f"加载预测数据失败: {e}")
            return []

    def calculate_stability_loss(self, current_pred: np.ndarray,
                               previous_pred: Optional[np.ndarray],
                               lambda_stability: float = 0.1) -> float:
        """
        计算稳定性损失

        Args:
            current_pred: 当前预测概率分布
            previous_pred: 上一次预测概率分布
            lambda_stability: 稳定性权重

        Returns:
            稳定性损失值
        """
        if previous_pred is None:
            return 0.0

        try:
            # 计算概率分布方差
            variance_penalty = np.sum(np.var([current_pred, previous_pred], axis=0))

            return lambda_stability * variance_penalty

        except Exception as e:
            print(f"稳定性损失计算失败: {e}")
            return 0.0

    def _apply_stability_constraint(self, prediction: Dict[str, Any],
                                  previous_prediction: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        应用稳定性约束

        Args:
            prediction: 当前预测结果
            previous_prediction: 上一次预测结果

        Returns:
            应用稳定性约束后的预测结果
        """
        if previous_prediction is None:
            return prediction

        try:
            # 获取当前和历史置信度
            current_confidence = prediction.get('confidence', 0.5)
            previous_confidence = previous_prediction.get('confidence', 0.5)

            # 计算置信度变化
            confidence_change = abs(current_confidence - previous_confidence)

            # 如果变化过大，应用稳定性调整
            if confidence_change > 0.3:  # 30%的变化阈值
                # 使用指数移动平均平滑置信度
                alpha = 0.7  # 平滑系数
                adjusted_confidence = alpha * current_confidence + (1 - alpha) * previous_confidence

                prediction['confidence'] = adjusted_confidence
                prediction['stability_adjusted'] = True
                prediction['original_confidence'] = current_confidence

            return prediction

        except Exception as e:
            print(f"稳定性约束应用失败: {e}")
            return prediction

    def _load_previous_prediction(self) -> Optional[Dict[str, Any]]:
        """
        加载上一次预测结果

        Returns:
            上一次预测结果
        """
        try:
            prediction_file = os.path.join(self.cache_dir, 'last_prediction.json')

            if os.path.exists(prediction_file):
                with open(prediction_file, 'r', encoding='utf-8') as f:
                    return json.load(f)

            return None

        except Exception as e:
            print(f"加载历史预测失败: {e}")
            return None

    def _save_current_prediction(self, prediction: Dict[str, Any]) -> None:
        """
        保存当前预测结果

        Args:
            prediction: 当前预测结果
        """
        try:
            prediction_file = os.path.join(self.cache_dir, 'last_prediction.json')

            # 只保存必要的信息
            save_data = {
                'numbers': prediction.get('numbers'),
                'confidence': prediction.get('confidence'),
                'timestamp': datetime.now().isoformat(),
                'candidates': prediction.get('candidates', [])[:5]  # 只保存前5个候选
            }

            with open(prediction_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存当前预测失败: {e}")

    def generate_fusion_prediction(self, data: Optional[List[str]] = None,
                                 max_candidates: int = 20,
                                 confidence_threshold: float = 0.5,
                                 context_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成融合预测 - 重构版本

        Args:
            data: 历史号码数据
            max_candidates: 最大候选数量
            confidence_threshold: 置信度阈值
            context_data: 上下文数据

        Returns:
            融合预测结果
        """
        try:
            # 如果没有提供数据，自动加载历史数据
            if not data:
                data = self._load_recent_data_for_prediction()

            # 收集各模型的预测结果
            model_predictions = {}

            # 获取传统模型预测
            trend_pred = self.generate_trend_predictions(data)
            if 'error' not in trend_pred:
                model_predictions['trend_analysis'] = trend_pred

            pattern_pred = self.generate_pattern_predictions(data)
            if 'error' not in pattern_pred:
                model_predictions['pattern_prediction'] = pattern_pred

            lstm_pred = self.generate_lstm_predictions(data)
            if 'error' not in lstm_pred:
                model_predictions['lstm_sequence'] = lstm_pred

            # 集成Phase 2.5优化模型预测
            if self.optimized_models_ready:
                optimized_pred = self.generate_optimized_predictions(data)
                if 'error' not in optimized_pred:
                    model_predictions['optimized_cnn_lstm'] = optimized_pred
                    print("✅ 优化模型预测已集成")

            if not model_predictions:
                return {'error': '没有可用的模型预测'}

            # 使用动态权重计算
            historical_performance = self._get_historical_performance()
            weights = self.calculate_dynamic_weights(model_predictions, historical_performance)

            # 执行融合，传递用户参数
            fusion_result = self.fusion_system.fuse_predictions(
                model_predictions,
                weights,
                max_candidates=max_candidates,
                confidence_threshold=confidence_threshold
            )

            return fusion_result

        except Exception as e:
            logger.error(f"生成融合预测失败: {e}")
            return {'error': f'融合预测生成失败: {e}'}

    def _convert_to_legacy_format(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """将新架构的结果转换为传统格式"""
        try:
            if 'fusion_prediction' in result:
                fusion_pred = result['fusion_prediction']
                return {
                    'numbers': fusion_pred.get('best_prediction', '000'),
                    'candidates': fusion_pred.get('candidates', []),
                    'confidence': fusion_pred.get('confidence', 0.5),
                    'weights': result.get('fusion_weights', {}),
                    'individual_predictions': result.get('individual_predictions', {}),
                    'validation': result.get('validation', {}),
                    'timestamp': result.get('metadata', {}).get('timestamp')
                }
            return result
        except Exception as e:
            logger.warning(f"转换结果格式失败: {e}")
            return result

    def _generate_fusion_prediction_legacy(self, data: List[str],
                                         max_candidates: int = 20,
                                         confidence_threshold: float = 0.5,
                                         context_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """传统的融合预测实现"""
        if not self.fusion_ready:
            return {'error': '融合系统未就绪'}

        try:
            # 如果没有提供数据，自动加载历史数据
            if not data:
                data = self._load_recent_data_for_prediction()

            # 计算期号敏感的融合策略
            period_sensitivity = self._calculate_period_sensitivity()

            # 收集各模型的预测结果
            model_predictions = {}

            # 获取趋势预测
            trend_pred = self.generate_trend_predictions(data)
            if 'error' not in trend_pred:
                # 应用期号敏感性调整
                trend_pred = self._apply_period_sensitivity(trend_pred, period_sensitivity)
                model_predictions['trend_analysis'] = trend_pred

            # 获取形态预测
            pattern_pred = self.generate_pattern_predictions(data)
            if 'error' not in pattern_pred:
                # 应用期号敏感性调整
                pattern_pred = self._apply_period_sensitivity(pattern_pred, period_sensitivity)
                model_predictions['pattern_prediction'] = pattern_pred

            # 获取LSTM序列预测
            lstm_pred = self.generate_lstm_predictions(data)
            if 'error' not in lstm_pred:
                # 应用期号敏感性调整
                lstm_pred = self._apply_period_sensitivity(lstm_pred, period_sensitivity)
                model_predictions['lstm_sequence'] = lstm_pred

            # 如果有创新特征模块，也可以加入
            if context_data and 'innovative_features' in context_data:
                model_predictions['innovative_features'] = context_data['innovative_features']
            
            if not model_predictions:
                return {'error': '没有可用的模型预测'}
            
            # 使用动态权重计算
            historical_performance = self._get_historical_performance()
            weights = self.calculate_dynamic_weights(model_predictions, historical_performance)
            
            # 执行融合，传递用户参数
            fusion_result = self.fusion_system.fuse_predictions(
                model_predictions,
                weights,
                max_candidates=max_candidates,
                confidence_threshold=confidence_threshold
            )

            if not fusion_result:
                return {'error': '融合预测失败'}

            # 添加融合元信息
            fusion_result['fusion_info'] = {
                'participating_models': list(model_predictions.keys()),
                'model_weights': weights,
                'fusion_method': 'adaptive_weighted',
                'max_candidates': max_candidates,
                'confidence_threshold': confidence_threshold
            }

            # 应用稳定性约束
            previous_prediction = self._load_previous_prediction()
            fusion_result = self._apply_stability_constraint(fusion_result, previous_prediction)

            # 保存当前预测用于下次稳定性检查
            self._save_current_prediction(fusion_result)

            # 质量监控（异步进行，不影响预测性能）
            try:
                candidates_numbers = [c.get('numbers') for c in fusion_result.get('candidates', []) if 'numbers' in c]
                if candidates_numbers:
                    monitor_result = self.quality_monitor.monitor_prediction_quality(
                        candidates_numbers, f"fusion_prediction_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    )

                    # 如果需要重训练，记录建议
                    if monitor_result.get('need_retrain', False):
                        fusion_result['quality_alert'] = {
                            'need_retrain': True,
                            'alerts': monitor_result.get('alerts', []),
                            'recommendations': monitor_result.get('recommendations', [])
                        }
            except Exception as e:
                print(f"质量监控失败: {e}")

            return fusion_result

        except Exception as e:
            return {'error': f'融合预测生成失败: {e}'}

    def _calculate_period_sensitivity(self) -> float:
        """
        计算期号敏感性因子

        Returns:
            期号敏感性因子 (0.9-1.1)
        """
        import random
        import time

        # 基于当前时间计算敏感性
        time_factor = (int(time.time()) % 60) / 60.0  # 0-1之间
        random_factor = random.random() * 0.1  # 0-0.1之间

        # 组合敏感性因子
        sensitivity = 0.9 + (time_factor * 0.1) + random_factor
        return min(max(sensitivity, 0.9), 1.1)

    def _apply_period_sensitivity(self, prediction: Dict[str, Any], sensitivity: float) -> Dict[str, Any]:
        """
        应用期号敏感性调整预测结果

        Args:
            prediction: 原始预测结果
            sensitivity: 敏感性因子

        Returns:
            调整后的预测结果
        """
        if not prediction or 'error' in prediction:
            return prediction

        # 调整主要预测的置信度
        if 'confidence' in prediction:
            prediction['confidence'] = prediction['confidence'] * sensitivity

        # 调整候选列表的置信度
        if 'candidates' in prediction and isinstance(prediction['candidates'], list):
            for candidate in prediction['candidates']:
                if isinstance(candidate, dict) and 'confidence' in candidate:
                    candidate['confidence'] = candidate['confidence'] * sensitivity

        # 添加敏感性标记
        prediction['period_sensitivity_applied'] = sensitivity

        return prediction

    def extract_intelligent_features(self, data: List[str],
                                   context_data: Dict[str, Any] = None) -> Dict[str, float]:
        """
        提取智能融合特征
        
        Args:
            data: 历史号码数据
            context_data: 上下文数据
            
        Returns:
            智能融合特征字典
        """
        if not self.models_trained:
            raise ValueError("模型未训练，请先调用train_all_models方法")
        
        features = {}
        context_data = context_data or {}
        
        # 1. 趋势特征
        trend_features = self._extract_trend_features(data, context_data)
        features.update(trend_features)
        
        # 2. 形态特征
        pattern_features = self._extract_pattern_features(data, context_data)
        features.update(pattern_features)
        
        # 3. 融合特征
        fusion_features = self._extract_fusion_features(data, context_data)
        features.update(fusion_features)
        
        # 4. 综合智能特征
        intelligent_features = self._extract_intelligent_features(features, data)
        features.update(intelligent_features)
        
        return features
    
    def _extract_trend_features(self, data: List[str], 
                               context_data: Dict[str, Any]) -> Dict[str, float]:
        """提取趋势特征"""
        features = {}
        
        try:
            if hasattr(self.trend_analyzer, 'trend_patterns'):
                trends = self.trend_analyzer.trend_patterns
                
                # 数字热度特征
                if 'digit_trends' in trends:
                    hot_count = sum(1 for info in trends['digit_trends'].values() 
                                  if info.get('heat_level') == 'hot')
                    cold_count = sum(1 for info in trends['digit_trends'].values() 
                                   if info.get('heat_level') == 'cold')
                    
                    features['trend_hot_digit_count'] = hot_count
                    features['trend_cold_digit_count'] = cold_count
                    features['trend_temperature_balance'] = abs(hot_count - cold_count) / 10
                
                # 形态趋势特征
                if 'combination_trends' in trends and 'form_trends' in trends['combination_trends']:
                    form_trends = trends['combination_trends']['form_trends']
                    for form, trend_info in form_trends.items():
                        features[f'trend_form_{form}_freq'] = trend_info.get('recent_frequency', 0)
                        features[f'trend_form_{form}_direction'] = 1 if trend_info.get('trend_direction') == 'rising' else 0
        
        except Exception as e:
            print(f"提取趋势特征时出错: {e}")
            # 返回默认特征
            for i in range(8):
                features[f'trend_feature_{i}'] = 0.0
        
        return features
    
    def _extract_pattern_features(self, data: List[str], 
                                 context_data: Dict[str, Any]) -> Dict[str, float]:
        """提取形态特征"""
        features = {}
        
        try:
            if hasattr(self.pattern_predictor, 'pattern_models'):
                patterns = self.pattern_predictor.pattern_models
                
                # 转换概率特征
                if 'transitions' in patterns and 'transition_probabilities' in patterns['transitions']:
                    trans_probs = patterns['transitions']['transition_probabilities']
                    
                    # 形态转换稳定性
                    if 'form_transitions' in trans_probs:
                        form_trans = trans_probs['form_transitions']
                        stability_scores = []
                        for current_form, next_probs in form_trans.items():
                            if next_probs:
                                max_prob = max(next_probs.values())
                                stability_scores.append(max_prob)
                        
                        if stability_scores:
                            features['pattern_form_stability'] = np.mean(stability_scores)
                            features['pattern_form_predictability'] = max(stability_scores)
                
                # 周期性特征
                if 'cycles' in patterns:
                    cycle_strengths = []
                    for cycle_type, cycle_info in patterns['cycles'].items():
                        if isinstance(cycle_info, dict) and 'cycle_strength' in cycle_info:
                            cycle_strengths.append(cycle_info['cycle_strength'])
                    
                    if cycle_strengths:
                        features['pattern_cycle_strength'] = max(cycle_strengths)
                        features['pattern_cycle_consistency'] = np.mean(cycle_strengths)
        
        except Exception as e:
            print(f"提取形态特征时出错: {e}")
            # 返回默认特征
            for i in range(6):
                features[f'pattern_feature_{i}'] = 0.0
        
        return features
    
    def _extract_fusion_features(self, data: List[str], 
                                context_data: Dict[str, Any]) -> Dict[str, float]:
        """提取融合特征"""
        features = {}
        
        try:
            if hasattr(self.fusion_system, 'fusion_models'):
                fusion_models = self.fusion_system.fusion_models
                
                # 模型权重特征
                if 'model_weights' in fusion_models:
                    weights = fusion_models['model_weights']
                    
                    # 权重分布特征
                    weight_values = list(weights.values())
                    if weight_values:
                        features['fusion_weight_entropy'] = -sum(w * np.log(w + 1e-8) for w in weight_values)
                        features['fusion_weight_max'] = max(weight_values)
                        features['fusion_weight_balance'] = 1 - np.std(weight_values)
                
                # 模型性能特征
                if 'model_performances' in fusion_models:
                    performances = fusion_models['model_performances']
                    
                    # 整体性能指标
                    all_accuracies = []
                    for model_perf in performances.values():
                        if 'exact_accuracy' in model_perf:
                            all_accuracies.append(model_perf['exact_accuracy'])
                    
                    if all_accuracies:
                        features['fusion_avg_accuracy'] = np.mean(all_accuracies)
                        features['fusion_best_accuracy'] = max(all_accuracies)
                        features['fusion_accuracy_variance'] = np.var(all_accuracies)
        
        except Exception as e:
            print(f"提取融合特征时出错: {e}")
            # 返回默认特征
            for i in range(6):
                features[f'fusion_feature_{i}'] = 0.0
        
        return features
    
    def _extract_intelligent_features(self, features: Dict[str, float], 
                                     data: List[str]) -> Dict[str, float]:
        """提取综合智能特征"""
        intelligent = {}
        
        try:
            # 特征交互项
            trend_hot = features.get('trend_hot_digit_count', 0)
            pattern_stability = features.get('pattern_form_stability', 0.5)
            fusion_accuracy = features.get('fusion_avg_accuracy', 0.1)
            
            # 智能融合置信度
            intelligent['intelligent_confidence'] = (pattern_stability + fusion_accuracy) / 2
            
            # 预测复杂度
            intelligent['intelligent_complexity'] = trend_hot * pattern_stability
            
            # 系统一致性
            weight_balance = features.get('fusion_weight_balance', 0.5)
            cycle_consistency = features.get('pattern_cycle_consistency', 0.5)
            intelligent['intelligent_consistency'] = (weight_balance + cycle_consistency) / 2
            
            # 特征稳定性
            feature_values = [v for k, v in features.items() if isinstance(v, (int, float)) and not np.isnan(v)]
            if feature_values:
                intelligent['intelligent_stability'] = 1 / (1 + np.std(feature_values))
                intelligent['intelligent_richness'] = len(feature_values)
            else:
                intelligent['intelligent_stability'] = 0.5
                intelligent['intelligent_richness'] = 0
        
        except Exception as e:
            print(f"提取智能特征时出错: {e}")
            # 返回默认特征
            for i in range(5):
                intelligent[f'intelligent_feature_{i}'] = 0.0
        
        return intelligent
    
    def get_system_summary(self) -> Dict[str, Any]:
        """
        获取系统摘要信息
        
        Returns:
            系统摘要信息
        """
        summary = {
            'models_trained': self.models_trained,
            'fusion_ready': self.fusion_ready,
            'available_components': {
                'trend_analyzer': hasattr(self.trend_analyzer, 'trend_patterns'),
                'pattern_predictor': hasattr(self.pattern_predictor, 'pattern_models'),
                'fusion_system': hasattr(self.fusion_system, 'fusion_models')
            }
        }
        
        if self.models_trained:
            # 统计各组件的能力
            capabilities = []
            
            if hasattr(self.trend_analyzer, 'trend_patterns'):
                capabilities.append('短期趋势捕捉')
            
            if hasattr(self.pattern_predictor, 'pattern_models'):
                capabilities.append('形态转换预测')
            
            if hasattr(self.fusion_system, 'fusion_models'):
                capabilities.append('自适应权重融合')
            
            summary['capabilities'] = capabilities
            summary['fusion_level'] = len(capabilities)
        
        return summary

    def validate_markov_model(self,
                             k_folds: int = 3,
                             data_limit: int = 1000) -> Dict[str, Any]:
        """
        验证马尔可夫模型性能

        Args:
            k_folds: 交叉验证折数
            data_limit: 数据限制

        Returns:
            验证结果
        """
        if not self.validation_enabled:
            return {'error': '模型验证已禁用'}

        try:
            print("🔍 开始马尔可夫模型验证...")

            # 使用当前模型参数进行验证
            from .markov_validator import MarkovModelValidator
            validator = MarkovModelValidator(self.db_path)

            result = validator.validate_markov_model(
                transition_window_size=1000,  # 使用优化后的参数
                probability_window_size=500,
                smoothing_alpha=1.0,
                k_folds=k_folds,
                data_limit=data_limit
            )

            # 更新验证状态
            self.last_validation_time = datetime.now()
            self.validation_results = result

            # 保存状态
            self._save_training_state()

            print("✅ 马尔可夫模型验证完成")
            return result

        except Exception as e:
            print(f"❌ 马尔可夫模型验证失败: {e}")
            return {'error': str(e)}

    def get_validation_status(self) -> Dict[str, Any]:
        """
        获取模型验证状态

        Returns:
            验证状态信息
        """
        return {
            'validation_enabled': self.validation_enabled,
            'last_validation_time': self.last_validation_time.isoformat() if self.last_validation_time else None,
            'has_validation_results': bool(self.validation_results),
            'validation_summary': {
                'total_predictions': self.validation_results.get('overall_results', {}).get('total_predictions', 0),
                'accuracy': self.validation_results.get('overall_results', {}).get('accuracy_metrics', {}).get('exact_match', 0.0),
                'diversity': self.validation_results.get('overall_results', {}).get('diversity_metrics', {}).get('simpson_diversity', 0.0)
            } if self.validation_results else {}
        }


if __name__ == "__main__":
    # 测试代码
    intelligent_system = IntelligentFusionSystem()
    
    try:
        # 训练所有模型
        training_result = intelligent_system.train_all_models()
        print("训练结果:", training_result)
        
        if training_result['success']:
            # 使用真实历史数据测试融合预测
            from core.database import DatabaseManager
            db_manager = DatabaseManager(intelligent_system.db_path)
            recent_records = db_manager.get_recent_records(10)
            test_data = [record.numbers for record in recent_records]

            fusion_prediction = intelligent_system.generate_fusion_prediction(test_data)
            print(f"\n融合预测结果:")
            if 'error' not in fusion_prediction:
                print(f"  预测号码: {fusion_prediction.get('numbers', 'N/A')}")
                print(f"  融合置信度: {fusion_prediction.get('confidence', 0):.3f}")
                print(f"  候选数量: {len(fusion_prediction.get('candidates', []))}")
            else:
                print(f"  错误: {fusion_prediction['error']}")
            
            # 获取系统摘要
            summary = intelligent_system.get_system_summary()
            print(f"\n系统摘要:")
            print(f"  融合就绪: {summary['fusion_ready']}")
            print(f"  可用能力: {summary.get('capabilities', [])}")
            print(f"  融合级别: {summary.get('fusion_level', 0)}")
            
    except Exception as e:
        print(f"测试失败: {e}")


