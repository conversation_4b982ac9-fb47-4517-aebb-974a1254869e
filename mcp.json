{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "disabled": false, "autoApprove": []}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": []}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"], "disabled": false, "autoApprove": []}, "knowledge-graph": {"command": "npx", "args": ["-y", "mcp-knowledge-graph", "--memory-path", "./knowledge-graph/memory.jsonl"], "disabled": false, "autoApprove": []}, "streamable-mcp-server": {"url": "http://127.0.0.1:12306/mcp", "type": "http", "disabled": false, "autoApprove": []}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "d:\\github\\3dyuce"], "disabled": false, "autoApprove": []}}}