"""
统一错误处理组件 - 增强版
提供一致的错误提示样式和处理逻辑，包含网络异常处理和重试机制
创建日期: 2025年7月31日
"""

import json
import os
import time
import traceback
import uuid
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, Optional, Union

import requests
import streamlit as st


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    PERMISSION_ERROR = "permission_error"
    DATA_ERROR = "data_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorHandler:
    """统一错误处理器 - 增强版"""

    def __init__(self):
        self.error_id = str(uuid.uuid4())
        self._init_session_state()

    def _init_session_state(self):
        """初始化会话状态"""
        if 'error_history' not in st.session_state:
            st.session_state.error_history = []

        if 'error_stats' not in st.session_state:
            st.session_state.error_stats = {
                'total_errors': 0,
                'network_errors': 0,
                'api_errors': 0,
                'validation_errors': 0
            }

    def show_network_error(self, retry_callback: Optional[Callable] = None,
                          error_details: Optional[str] = None):
        """显示网络错误提示

        Args:
            retry_callback: 重试回调函数
            error_details: 错误详细信息
        """
        self._log_error(ErrorType.NETWORK_ERROR, error_details)

        st.error("🌐 网络连接异常")

        with st.expander("📋 错误详情", expanded=False):
            st.write("**错误类型**: 网络连接失败")
            st.write("**可能原因**:")
            st.write("- 网络连接不稳定")
            st.write("- 服务器暂时不可用")
            st.write("- 防火墙或代理设置问题")

            if error_details:
                st.write("**技术详情**:")
                st.code(error_details)

        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("🔄 重试", key=f"retry_{self.error_id}"):
                if retry_callback:
                    try:
                        retry_callback()
                        st.success("✅ 重试成功！")
                        st.rerun()
                    except Exception as e:
                        st.error(f"重试失败: {str(e)}")

        with col2:
            if st.button("📊 检查状态", key=f"status_{self.error_id}"):
                self._show_network_status()

        with col3:
            st.info("💡 建议：检查网络连接后重试")

    def show_api_error(self, error_message: str, status_code: Optional[int] = None,
                      endpoint: Optional[str] = None):
        """显示API错误提示

        Args:
            error_message: 错误消息
            status_code: HTTP状态码
            endpoint: API端点
        """
        self._log_error(ErrorType.API_ERROR, f"Status: {status_code}, Message: {error_message}")

        if status_code:
            if status_code == 404:
                st.error("🔍 请求的资源不存在")
            elif status_code == 500:
                st.error("⚠️ 服务器内部错误")
            elif status_code == 403:
                st.error("🚫 访问被拒绝")
            elif status_code == 429:
                st.error("⏱️ 请求过于频繁，请稍后重试")
            else:
                st.error(f"❌ API错误 (状态码: {status_code})")
        else:
            st.error("❌ API请求失败")

        with st.expander("📋 错误详情", expanded=False):
            st.write("**错误消息**:", error_message)
            if status_code:
                st.write("**状态码**:", status_code)
            if endpoint:
                st.write("**请求端点**:", endpoint)

            # 提供解决建议
            if status_code == 404:
                st.info("💡 建议：检查请求的URL是否正确")
            elif status_code == 500:
                st.info("💡 建议：服务器问题，请稍后重试")
            elif status_code == 403:
                st.info("💡 建议：检查访问权限或认证信息")
            elif status_code == 429:
                st.info("💡 建议：等待一段时间后重试")

    def show_validation_error(self, field_name: str, error_details: str):
        """显示验证错误提示

        Args:
            field_name: 字段名称
            error_details: 错误详情
        """
        self._log_error(ErrorType.VALIDATION_ERROR, f"Field: {field_name}, Details: {error_details}")

        st.error(f"📝 输入验证失败: {field_name}")

        with st.expander("📋 验证详情", expanded=True):
            st.write("**字段**:", field_name)
            st.write("**错误信息**:", error_details)

            # 常见验证错误的建议
            if "required" in error_details.lower():
                st.info("💡 此字段为必填项，请提供有效值")
            elif "format" in error_details.lower():
                st.info("💡 请检查输入格式是否正确")
            elif "range" in error_details.lower():
                st.info("💡 请确保输入值在有效范围内")

    def _show_network_status(self):
        """显示网络状态检查"""
        with st.spinner("检查网络状态..."):
            try:
                # 检查API服务状态
                response = requests.get("http://127.0.0.1:8888/health", timeout=5)
                if response.status_code == 200:
                    st.success("✅ API服务正常")
                else:
                    st.error(f"❌ API服务异常 (状态码: {response.status_code})")
            except requests.ConnectionError:
                st.error("❌ 无法连接到API服务")
            except requests.Timeout:
                st.error("❌ API服务响应超时")
            except Exception as e:
                st.error(f"❌ 网络检查失败: {str(e)}")

    def _log_error(self, error_type: ErrorType, details: Optional[str] = None):
        """记录错误到会话状态

        Args:
            error_type: 错误类型
            details: 错误详情
        """
        error_record = {
            'id': self.error_id,
            'type': error_type.value,
            'details': details,
            'timestamp': time.time(),
            'formatted_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        st.session_state.error_history.append(error_record)
        st.session_state.error_stats['total_errors'] += 1

        # 更新特定类型错误计数
        if error_type == ErrorType.NETWORK_ERROR:
            st.session_state.error_stats['network_errors'] += 1
        elif error_type == ErrorType.API_ERROR:
            st.session_state.error_stats['api_errors'] += 1
        elif error_type == ErrorType.VALIDATION_ERROR:
            st.session_state.error_stats['validation_errors'] += 1

    @staticmethod
    def show_error(
        title: str,
        message: str,
        error_type: str = "error",
        show_details: bool = False,
        exception: Optional[Exception] = None,
        suggestions: Optional[list] = None
    ):
        """
        显示统一格式的错误信息
        
        Args:
            title: 错误标题
            message: 错误消息
            error_type: 错误类型 (error, warning, info)
            show_details: 是否显示详细信息
            exception: 异常对象
            suggestions: 解决建议列表
        """
        # 添加统一的错误样式
        st.markdown("""
        <style>
        .error-container {
            border-left: 4px solid #ff4b4b;
            padding: 1rem;
            margin: 1rem 0;
            background-color: #fff2f2;
            border-radius: 0.5rem;
        }
        .warning-container {
            border-left: 4px solid #ffa500;
            padding: 1rem;
            margin: 1rem 0;
            background-color: #fff8e1;
            border-radius: 0.5rem;
        }
        .info-container {
            border-left: 4px solid #1f77b4;
            padding: 1rem;
            margin: 1rem 0;
            background-color: #e3f2fd;
            border-radius: 0.5rem;
        }
        .error-title {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 0.5rem;
        }
        .error-message {
            margin-bottom: 1rem;
        }
        .suggestion-item {
            margin: 0.25rem 0;
            padding-left: 1rem;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # 根据错误类型选择容器样式
        container_class = f"{error_type}-container"
        
        # 选择合适的图标
        icons = {
            "error": "❌",
            "warning": "⚠️", 
            "info": "ℹ️"
        }
        icon = icons.get(error_type, "❌")
        
        # 显示错误信息
        st.markdown(f"""
        <div class="{container_class}">
            <div class="error-title">{icon} {title}</div>
            <div class="error-message">{message}</div>
        </div>
        """, unsafe_allow_html=True)
        
        # 显示解决建议
        if suggestions:
            st.markdown("**🔧 解决建议:**")
            for suggestion in suggestions:
                st.markdown(f"<div class='suggestion-item'>• {suggestion}</div>", 
                          unsafe_allow_html=True)
        
        # 显示详细信息
        if show_details and exception:
            with st.expander("🔍 详细错误信息"):
                st.code(f"错误类型: {type(exception).__name__}")
                st.code(f"错误信息: {str(exception)}")
                if hasattr(exception, '__traceback__'):
                    st.code(f"错误堆栈:\n{traceback.format_exc()}")
    
    @staticmethod
    def show_api_error(endpoint: str, status_code: int, message: str):
        """显示API错误"""
        ErrorHandler.show_error(
            title="API调用失败",
            message=f"端点: {endpoint}\n状态码: {status_code}\n错误: {message}",
            error_type="error",
            suggestions=[
                "检查API服务是否正在运行",
                "确认网络连接正常",
                "验证请求参数是否正确",
                "查看API服务日志获取更多信息"
            ]
        )
    
    @staticmethod
    def show_data_error(operation: str, details: str):
        """显示数据操作错误"""
        ErrorHandler.show_error(
            title="数据操作失败",
            message=f"操作: {operation}\n详情: {details}",
            error_type="error",
            suggestions=[
                "检查数据库连接状态",
                "确认数据格式是否正确",
                "验证数据权限设置",
                "尝试重新加载数据"
            ]
        )
    
    @staticmethod
    def show_model_error(model_name: str, error_msg: str):
        """显示模型错误"""
        ErrorHandler.show_error(
            title="模型加载/预测失败",
            message=f"模型: {model_name}\n错误: {error_msg}",
            error_type="error",
            suggestions=[
                "检查模型文件是否存在",
                "确认模型格式是否正确",
                "验证输入数据格式",
                "重新训练或下载模型"
            ]
        )
    
    @staticmethod
    def show_success(message: str, details: Optional[str] = None):
        """显示成功信息"""
        st.success(f"✅ {message}")
        if details:
            st.info(details)
    
    @staticmethod
    def show_warning(message: str, suggestions: Optional[list] = None):
        """显示警告信息"""
        ErrorHandler.show_error(
            title="注意",
            message=message,
            error_type="warning",
            suggestions=suggestions
        )
    
    @staticmethod
    def show_info(message: str, details: Optional[str] = None):
        """显示信息提示"""
        ErrorHandler.show_error(
            title="信息",
            message=message,
            error_type="info"
        )
        if details:
            st.info(details)


class ErrorLogger:
    """错误日志记录器"""
    
    @staticmethod
    def log_error(error: Exception, context: Optional[Dict[str, Any]] = None):
        """
        记录错误到日志

        Args:
            error: 异常对象
            context: 上下文信息
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        error_info = {
            "timestamp": timestamp,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
            "context": context or {}
        }

        # 写入日志文件
        try:
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)

            log_file = log_dir / f"error_{datetime.now().strftime('%Y%m%d')}.log"
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"[ERROR] {timestamp}: {json.dumps(error_info, ensure_ascii=False, indent=2)}\n")
        except Exception:
            pass  # 静默处理日志写入错误

        # 控制台输出
        print(f"[ERROR] {timestamp}: {error_info}")

        return error_info


class ErrorRecovery:
    """错误恢复机制"""

    @staticmethod
    def show_network_error_recovery():
        """显示网络错误恢复选项"""
        st.error("🌐 网络连接错误")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 重试连接"):
                st.rerun()

        with col2:
            if st.button("🔍 检查服务状态"):
                ErrorRecovery._check_api_service()

        with col3:
            if st.button("📋 查看诊断信息"):
                ErrorRecovery._show_network_diagnostic()

    @staticmethod
    def show_data_error_recovery(operation: str):
        """显示数据错误恢复选项"""
        st.error(f"📊 数据操作失败: {operation}")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 重新加载数据"):
                st.rerun()

        with col2:
            if st.button("🔍 检查数据源"):
                ErrorRecovery._check_data_source()

        with col3:
            if st.button("⚙️ 数据修复"):
                ErrorRecovery._show_data_repair_options()

    @staticmethod
    def show_model_error_recovery(model_name: str):
        """显示模型错误恢复选项"""
        st.error(f"🤖 模型错误: {model_name}")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 重新加载模型"):
                st.rerun()

        with col2:
            if st.button("🔍 检查模型状态"):
                ErrorRecovery._check_model_status(model_name)

        with col3:
            if st.button("⚙️ 模型诊断"):
                ErrorRecovery._show_model_diagnostic(model_name)

    @staticmethod
    def _check_api_service():
        """检查API服务状态"""
        st.subheader("🔍 API服务检查")

        try:
            response = requests.get("http://127.0.0.1:8888/health", timeout=5)
            if response.status_code == 200:
                st.success("✅ API服务正常运行")
                health_data = response.json()
                st.json(health_data)
            else:
                st.error(f"❌ API服务响应异常: HTTP {response.status_code}")
        except requests.exceptions.ConnectionError:
            st.error("❌ 无法连接到API服务")
            st.info("请确保API服务在127.0.0.1:8888端口运行")
            st.code("python start_production_api.py")
        except Exception as e:
            st.error(f"❌ API服务检查失败: {str(e)}")

    @staticmethod
    def _check_data_source():
        """检查数据源状态"""
        st.subheader("📊 数据源检查")

        try:
            response = requests.head("https://data.17500.cn/3d_asc.txt", timeout=10)
            if response.status_code == 200:
                st.success("✅ 数据源可访问")
                file_size = response.headers.get('content-length', 'N/A')
                if file_size != 'N/A':
                    st.info(f"数据文件大小: {int(file_size) / 1024:.1f} KB")
            else:
                st.warning(f"⚠️ 数据源响应异常: HTTP {response.status_code}")
        except Exception as e:
            st.error(f"❌ 无法访问数据源: {str(e)}")

    @staticmethod
    def _show_network_diagnostic():
        """显示网络诊断信息"""
        st.subheader("🌐 网络诊断")

        # 检查本地服务
        services = [
            ("Streamlit Web服务", "http://127.0.0.1:8501"),
            ("FastAPI后端服务", "http://127.0.0.1:8888/health"),
        ]

        for service_name, url in services:
            try:
                response = requests.get(url, timeout=3)
                if response.status_code == 200:
                    st.success(f"✅ {service_name}: 正常")
                else:
                    st.error(f"❌ {service_name}: HTTP {response.status_code}")
            except Exception:
                st.error(f"❌ {service_name}: 无法连接")

        # 检查外部数据源
        try:
            response = requests.head("https://data.17500.cn/3d_asc.txt", timeout=5)
            if response.status_code == 200:
                st.success("✅ 外部数据源: 正常")
            else:
                st.warning(f"⚠️ 外部数据源: HTTP {response.status_code}")
        except Exception:
            st.error("❌ 外部数据源: 无法访问")

    @staticmethod
    def _show_data_repair_options():
        """显示数据修复选项"""
        st.subheader("⚙️ 数据修复选项")

        st.info("以下操作可能有助于修复数据问题：")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 重新下载数据"):
                st.info("正在重新下载数据...")
                # 这里可以添加实际的数据重新下载逻辑

        with col2:
            if st.button("🗃️ 重建数据库"):
                st.info("正在重建数据库...")
                # 这里可以添加实际的数据库重建逻辑

    @staticmethod
    def _check_model_status(model_name: str):
        """检查模型状态"""
        st.subheader(f"🤖 模型状态检查: {model_name}")

        try:
            response = requests.get("http://127.0.0.1:8888/api/v1/models/status", timeout=5)
            if response.status_code == 200:
                models_data = response.json()
                st.json(models_data)
            else:
                st.error(f"❌ 无法获取模型状态: HTTP {response.status_code}")
        except Exception as e:
            st.error(f"❌ 模型状态检查失败: {str(e)}")

    @staticmethod
    def _show_model_diagnostic(model_name: str):
        """显示模型诊断信息"""
        st.subheader(f"🔍 模型诊断: {model_name}")

        # 检查模型文件
        model_paths = [
            f"models/{model_name}.pth",
            f"models/{model_name}.pkl",
            f"models/{model_name}/"
        ]

        st.write("**模型文件检查:**")
        for path in model_paths:
            if os.path.exists(path):
                st.success(f"✅ {path}")
            else:
                st.error(f"❌ {path} (不存在)")

        # 检查模型API
        try:
            response = requests.get(f"http://127.0.0.1:8888/api/v1/models/{model_name}/info", timeout=5)
            if response.status_code == 200:
                st.success("✅ 模型API响应正常")
                model_info = response.json()
                st.json(model_info)
            else:
                st.error(f"❌ 模型API响应异常: HTTP {response.status_code}")
        except Exception as e:
            st.error(f"❌ 模型API检查失败: {str(e)}")


class SmartErrorHandler:
    """智能错误处理器"""

    @staticmethod
    def handle_exception(func):
        """装饰器：智能异常处理"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except requests.exceptions.ConnectionError as e:
                ErrorHandler.show_api_error("连接失败", 0, str(e))
                ErrorRecovery.show_network_error_recovery()
            except requests.exceptions.Timeout as e:
                ErrorHandler.show_error(
                    title="请求超时",
                    message=f"请求超时: {str(e)}",
                    error_type="warning",
                    suggestions=[
                        "检查网络连接速度",
                        "增加请求超时时间",
                        "重试请求"
                    ]
                )
            except requests.exceptions.HTTPError as e:
                response = e.response
                ErrorHandler.show_api_error(
                    response.url,
                    response.status_code,
                    response.text
                )
            except ImportError as e:
                ErrorHandler.show_error(
                    title="模块导入错误",
                    message=f"无法导入模块: {str(e)}",
                    error_type="error",
                    suggestions=[
                        "检查模块文件是否存在",
                        "确认文件路径是否正确",
                        "验证模块依赖是否安装"
                    ]
                )
            except FileNotFoundError as e:
                ErrorHandler.show_error(
                    title="文件未找到",
                    message=f"文件不存在: {str(e)}",
                    error_type="error",
                    suggestions=[
                        "检查文件路径是否正确",
                        "确认文件是否已创建",
                        "验证文件权限设置"
                    ]
                )
            except Exception as e:
                ErrorHandler.show_error(
                    title="未知错误",
                    message=f"发生未知错误: {str(e)}",
                    error_type="error",
                    show_details=True,
                    exception=e,
                    suggestions=[
                        "刷新页面重试",
                        "检查系统日志",
                        "联系技术支持"
                    ]
                )
                ErrorLogger.log_error(e, {
                    "function": func.__name__,
                    "args": str(args),
                    "kwargs": str(kwargs)
                })

        return wrapper

    @staticmethod
    def safe_api_call(url: str, method: str = "GET", **kwargs):
        """安全的API调用"""
        try:
            if method.upper() == "GET":
                response = requests.get(url, **kwargs)
            elif method.upper() == "POST":
                response = requests.post(url, **kwargs)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.ConnectionError:
            ErrorHandler.show_api_error(url, 0, "连接失败")
            ErrorRecovery.show_network_error_recovery()
            return None
        except requests.exceptions.Timeout:
            ErrorHandler.show_error(
                title="请求超时",
                message=f"API请求超时: {url}",
                error_type="warning"
            )
            return None
        except requests.exceptions.HTTPError as e:
            ErrorHandler.show_api_error(url, e.response.status_code, str(e))
            return None
        except Exception as e:
            ErrorHandler.show_error(
                title="API调用失败",
                message=f"调用API时发生错误: {str(e)}",
                error_type="error"
            )
            ErrorLogger.log_error(e, {"url": url, "method": method})
            return None


# 增强的便捷函数
def safe_api_request_enhanced(url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
    """增强的安全API请求包装器

    Args:
        url: 请求URL
        method: HTTP方法
        **kwargs: requests参数

    Returns:
        响应对象或None
    """
    error_handler = ErrorHandler()

    try:
        if method.upper() == 'GET':
            response = requests.get(url, **kwargs)
        elif method.upper() == 'POST':
            response = requests.post(url, **kwargs)
        elif method.upper() == 'PUT':
            response = requests.put(url, **kwargs)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, **kwargs)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")

        if response.status_code >= 400:
            ErrorHandler.show_api_error(
                endpoint=url,
                status_code=response.status_code,
                message=response.text
            )
            return None

        return response

    except requests.ConnectionError:
        error_handler.show_network_error(
            retry_callback=lambda: safe_api_request_enhanced(url, method, **kwargs),
            error_details="连接错误"
        )
        return None
    except requests.Timeout:
        st.error("⏱️ 请求超时，请稍后重试")
        return None
    except Exception as e:
        ErrorHandler.show_api_error(
            endpoint=url,
            status_code=0,
            message=str(e)
        )
        return None

def show_enhanced_error_summary():
    """显示增强的错误统计摘要"""
    if hasattr(st.session_state, 'error_stats') and st.session_state.error_stats.get('total_errors', 0) > 0:
        st.subheader("📊 错误统计")

        stats = st.session_state.error_stats
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("总错误数", stats.get('total_errors', 0))
        with col2:
            st.metric("网络错误", stats.get('network_errors', 0))
        with col3:
            st.metric("API错误", stats.get('api_errors', 0))
        with col4:
            st.metric("验证错误", stats.get('validation_errors', 0))

# 全局增强错误处理器实例
global_enhanced_error_handler = ErrorHandler()
